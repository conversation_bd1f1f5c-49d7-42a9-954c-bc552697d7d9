# LangChain4j-AIDeepin 工作流引擎分析

## 项目概述

本项目深入分析了LangChain4j-AIDeepin的工作流引擎实现，重点关注工作流在编译前后的状态变化，以及如何集成外部请求、MCP功能和LLM/RAG服务。

## 核心文档

### 📊 可视化流程图
- **[工作流引擎编译前后状态变化详解.canvas](./工作流引擎编译前后状态变化详解.canvas)**
  - Obsidian Advanced Canvas格式的详细流程图
  - 展示编译前后的完整状态变化
  - 包含外部集成和技术实现细节

### 📖 技术实现文档
- **[工作流引擎技术实现详解.md](./工作流引擎技术实现详解.md)**
  - 详细的技术实现分析
  - 代码示例和配置说明
  - 架构设计和最佳实践

- **[工作流节点MCP和RAG底层实现机制详解.md](./工作流节点MCP和RAG底层实现机制详解.md)**
  - MCP Function Call的完整调用链路
  - RAG向量检索的底层实现机制
  - 工作流节点如何保证功能调用的可靠性

### 📊 深度技术流程图
- **[MCP和RAG底层调用机制详解.canvas](./MCP和RAG底层调用机制详解.canvas)**
  - MCP客户端生命周期管理
  - 工具发现、注册、执行的完整流程
  - RAG检索器创建和向量搜索过程
  - Neo4j向量存储的底层实现

## 主要发现

### 1. 工作流状态变化

#### 编译前：数据库定义状态
- 静态存储在数据库表中
- 平面化的节点和边关系
- JSON配置的灵活性
- 无执行能力

#### 编译后：可执行状态图
- 内存中的状态机结构
- 支持并行执行和条件分支
- 实时状态监控
- 中断恢复机制

### 2. 核心技术特性

#### 🔄 工作流编译过程
1. **构建编译节点树**: `buildCompileNode()` - 分析依赖关系，识别并行和条件分支
2. **生成状态图**: `buildStateGraph()` - 转换为LangGraph4j状态图
3. **编译执行图**: `mainStateGraph.compile()` - 生成可执行的状态机

#### 🌐 外部请求集成
- **HttpRequestNode**: 完整的RESTful API调用支持
- **模板变量系统**: `{input}`, `{nodeUuid.paramName}` 等灵活变量替换
- **重试机制**: 自动重试和错误处理
- **多种内容类型**: JSON、Form、Multipart等

#### 🤖 LLM集成实现
- **流式处理**: `StreamingChatGenerator` 实时响应
- **多模型支持**: 动态模型选择
- **Token计费**: 自动统计和缓存
- **模板渲染**: 智能提示词构建

#### 🔍 RAG检索功能
- **向量检索**: 基于相似度的智能搜索
- **知识库隔离**: 元数据过滤机制
- **分数阈值**: 相关性控制
- **默认响应**: 兜底机制

#### 🛠️ MCP功能集成
- **双传输方式**: SSE和STDIO支持
- **工具自动发现**: `mcpClient.listTools()`
- **智能调用**: LLM根据对话内容自动选择工具
- **递归处理**: 工具调用结果自动反馈给LLM
- **参数加密**: AES加密存储
- **权限控制**: 用户级别隔离

#### 🔍 MCP Function Call 调用链路
1. **客户端创建**: `UserMcpService.createMcpClients()` - 根据传输类型创建MCP客户端
2. **工具发现**: `mcpClient.listTools()` - 动态发现可用工具规范
3. **工具注册**: `McpToolProvider` - 将工具自动注册到LLM的工具列表
4. **智能调用**: LLM根据对话内容自动选择和调用工具
5. **执行处理**: `selectedMcpClient.executeTool(req)` - 执行具体工具调用
6. **递归反馈**: 工具执行结果自动添加到消息历史，LLM继续处理

#### 🔍 RAG检索底层机制
1. **检索器创建**: `EmbeddingRAG.createRetriever()` - 构建带元数据过滤的检索器
2. **查询向量化**: `embeddingModel.embed(query.text())` - 将查询文本转换为向量
3. **向量搜索**: `embeddingStore.search(searchRequest)` - 执行相似度搜索
4. **元数据过滤**: 通过Filter机制实现知识库级别隔离
5. **结果处理**: 基于相似度分数过滤和聚合检索结果
6. **兜底机制**: 严格模式控制和默认响应处理

### 3. 架构优势

#### 🏗️ 设计模式
- **工厂模式**: `WfNodeFactory` 统一节点创建
- **状态机模式**: 基于LangGraph4j的状态管理
- **观察者模式**: SSE实时状态推送
- **模板方法模式**: `AbstractWfNode` 统一生命周期

#### ⚡ 性能优化
- **并行执行**: 子图独立编译和执行
- **流式处理**: 实时数据流转
- **资源池化**: 连接池和线程池
- **内存管理**: 智能缓存和清理

#### 🔒 安全特性
- **用户隔离**: 工作流按用户隔离
- **参数加密**: 敏感信息AES加密
- **权限控制**: 多层级访问控制
- **执行沙箱**: 进程隔离和资源限制

## 技术栈

- **核心框架**: Spring Boot + LangChain4j
- **状态图引擎**: LangGraph4j
- **数据库**: PostgreSQL + MyBatis Plus
- **缓存**: Redis
- **消息推送**: SSE (Server-Sent Events)
- **HTTP客户端**: Apache HttpClient
- **向量数据库**: 支持多种向量存储
- **协议支持**: MCP (Model Context Protocol)

## 使用场景

### 1. 智能客服工作流
```
用户输入 → 意图识别 → 知识库检索 → LLM生成回答 → 结果输出
```

### 2. 文档处理流水线
```
文档上传 → 内容提取 → 关键词提取 → 向量化存储 → 索引构建
```

### 3. 多模态内容生成
```
文本输入 → LLM分析 → 图像生成 → 内容合成 → 质量检查 → 输出
```

### 4. 外部API集成
```
数据获取 → API调用 → 结果处理 → 数据转换 → 存储/推送
```

## 项目结构

```
langchain4j-aideepin-main/
├── adi-common/                 # 核心业务逻辑
│   └── workflow/              # 工作流引擎
│       ├── WorkflowEngine.java    # 核心执行引擎
│       ├── node/                  # 各种节点实现
│       └── data/                  # 数据结构定义
├── adi-admin/                 # 管理后台
└── docs/                      # 文档和配置
```

## 贡献

本分析基于对LangChain4j-AIDeepin项目的深入研究，旨在帮助开发者理解工作流引擎的设计思路和实现细节。

## 许可证

本分析文档遵循原项目的许可证条款。