<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.moyz</groupId>
        <artifactId>aideepin</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>adi-chat</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.moyz</groupId>
            <artifactId>adi-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!-- 引入本地lib包 -->
        <dependency>
            <groupId>com.ramostear</groupId>
            <artifactId>Happy-Captcha</artifactId>
            <scope>system</scope>
            <version>1.0.1</version>
            <systemPath>${project.basedir}/src/lib/ADI-Happy-Captcha-1.0.1.jar</systemPath>
        </dependency>

    </dependencies>

</project>