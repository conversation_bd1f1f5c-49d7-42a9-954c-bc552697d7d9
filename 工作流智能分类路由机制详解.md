# 工作流智能分类路由机制详解

## 📋 概述

项目中**并没有实现传统意义上的多Agent协作路由**，而是通过 `ClassifierNode`（分类器节点）实现基于LLM的智能分类路由。这种机制通过AI模型对输入内容进行智能分类，然后根据分类结果动态路由到不同的处理分支。

## 🏗️ 核心架构

### 1. ClassifierNode - AI智能分类器

```java
/**
 * 【节点】问题分类器
 * 使用LLM对输入内容进行智能分类，根据分类结果路由到不同的下游节点
 */
@Slf4j
public class ClassifierNode extends AbstractWfNode {
    
    @Override
    protected NodeProcessResult onProcess() {
        // 1. 获取分类配置
        ClassifierNodeConfig nodeConfig = checkAndGetConfig(ClassifierNodeConfig.class);
        
        // 2. 构建分类提示词
        String prompt = ClassifierPrompt.createPrompt(
            inputText, 
            nodeConfig.getCategories()
        );
        
        // 3. 调用LLM进行分类
        NodeIOData llmResponse = WorkflowUtil.invokeLLM(
            wfState, 
            nodeConfig.getModelName(), 
            prompt
        );
        
        // 4. 解析分类结果并路由
        ClassifierLLMResp classifierResp = JsonUtil.fromJson(
            llmResponse.valueToString(), 
            ClassifierLLMResp.class
        );
        
        // 5. 确定目标节点
        String targetNodeUuid = findTargetNode(classifierResp.getCategoryUuid());
        
        return NodeProcessResult.builder()
            .nextNodeUuid(targetNodeUuid)
            .content(result)
            .build();
    }
}
```

### 2. 配置数据结构

```java
@Data
public class ClassifierNodeConfig {
    private List<ClassifierCategory> categories = new ArrayList<>();
    @JsonProperty("model_name")
    private String modelName;  // 用于分类的LLM模型
}

@Data
public class ClassifierCategory {
    @JsonProperty("category_uuid")
    private String categoryUuid;      // 分类唯一标识
    
    @JsonProperty("category_name") 
    private String categoryName;      // 分类名称和描述
    
    @JsonProperty("target_node_uuid")
    private String targetNodeUuid;    // 🔥 路由目标节点
}
```

## 🎯 智能路由执行流程

### 步骤1：前端配置分类器

```html
<div class="classifier-config-panel">
  <h3>AI智能分类器配置</h3>
  
  <!-- 模型选择 -->
  <div class="model-selection">
    <label>分类模型：</label>
    <select v-model="config.modelName">
      <option value="gpt-4">GPT-4</option>
      <option value="deepseek-chat">DeepSeek Chat</option>
    </select>
  </div>

  <!-- 分类类别配置 -->
  <div class="categories-config">
    <div v-for="category in config.categories" class="category-item">
      <input v-model="category.categoryName" placeholder="分类描述" />
      <select v-model="category.targetNodeUuid">
        <option value="tech-support">技术支持流程</option>
        <option value="sales-consultation">销售咨询流程</option>
        <option value="complaint-handling">投诉处理流程</option>
      </select>
    </div>
  </div>
</div>
```

### 步骤2：分类提示词生成

```java
public class ClassifierPrompt {
    
    public static String createPrompt(String inputText, List<ClassifierCategory> categories) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下用户输入进行分类，从给定的类别中选择最合适的一个：\n\n");
        prompt.append("用户输入：").append(inputText).append("\n\n");
        prompt.append("可选类别：\n");
        
        for (ClassifierCategory category : categories) {
            prompt.append("- ").append(category.getCategoryUuid())
                  .append(": ").append(category.getCategoryName()).append("\n");
        }
        
        prompt.append("\n请以JSON格式返回分类结果：\n");
        prompt.append("{\n");
        prompt.append("  \"category_uuid\": \"选中的类别UUID\",\n");
        prompt.append("  \"category_name\": \"选中的类别名称\",\n");
        prompt.append("  \"confidence\": 0.95\n");
        prompt.append("}");
        
        return prompt.toString();
    }
}
```

### 步骤3：编译时路由构建

```java
private void buildStateGraph(StateGraph<WfNodeState> stateGraph, CompileNode compileNode) {
    String nodeUuid = compileNode.getId();
    
    // 添加分类器节点
    stateGraph.addNode(nodeUuid, node_async(state -> runNode(findNodeByUuid(nodeUuid), state)));
    
    // 检查是否为分类器节点（条件路由）
    if (compileNode.isConditional()) {
        // 构建路由映射表
        Map<String, String> routingMap = new HashMap<>();
        for (CompileNode nextNode : compileNode.getNextNodes()) {
            routingMap.put(nextNode.getId(), nextNode.getId());
        }
        
        // 添加动态路由边
        stateGraph.addConditionalEdges(
            nodeUuid,
            // 🔥 动态路由决策函数
            edge_async(state -> {
                String targetNodeUuid = state.data().get("next").toString();
                log.info("🤖 AI分类路由决策: {} -> {}", nodeUuid, targetNodeUuid);
                return targetNodeUuid;
            }),
            routingMap
        );
    }
}
```

## 📝 实际应用示例

### 智能客服分类路由

**配置示例**：
```json
{
  "modelName": "gpt-4",
  "categories": [
    {
      "categoryUuid": "tech-001",
      "categoryName": "技术问题 - 包括软件故障、系统错误、功能使用等",
      "targetNodeUuid": "tech-support-workflow"
    },
    {
      "categoryUuid": "sales-002", 
      "categoryName": "销售咨询 - 包括产品价格、功能对比、购买流程等",
      "targetNodeUuid": "sales-consultation-workflow"
    },
    {
      "categoryUuid": "complaint-003",
      "categoryName": "投诉建议 - 包括服务不满、功能建议、改进意见等", 
      "targetNodeUuid": "complaint-handling-workflow"
    }
  ]
}
```

**执行过程**：

**用户输入**："我的软件总是闪退，无法正常使用，请帮我解决"

```java
// 1. 构建分类提示词
String prompt = """
请对以下用户输入进行分类，从给定的类别中选择最合适的一个：

用户输入：我的软件总是闪退，无法正常使用，请帮我解决

可选类别：
- tech-001: 技术问题 - 包括软件故障、系统错误、功能使用等
- sales-002: 销售咨询 - 包括产品价格、功能对比、购买流程等  
- complaint-003: 投诉建议 - 包括服务不满、功能建议、改进意见等

请以JSON格式返回分类结果：
{
  "category_uuid": "选中的类别UUID",
  "category_name": "选中的类别名称", 
  "confidence": 0.95
}
""";

// 2. LLM分类执行
NodeIOData llmResponse = WorkflowUtil.invokeLLM(wfState, "gpt-4", prompt);

// 3. LLM返回结果
String llmResult = """
{
  "category_uuid": "tech-001",
  "category_name": "技术问题",
  "confidence": 0.98
}
""";

// 4. 路由到技术支持工作流
String targetNodeUuid = "tech-support-workflow";
```

## 🔄 路由执行机制

### 状态图中的条件路由

```java
// 编译后的路由器
edge_async(state -> {
    // 从分类器执行结果中获取目标节点
    String classification = state.data().get("classification").toString();
    String targetNode = state.data().get("next").toString();

    log.info("🤖 AI分类结果: {}", classification);
    log.info("🔀 路由到节点: {}", targetNode);

    return targetNode;  // 返回目标节点UUID
})
```

### 执行日志示例

```
2024-01-15 14:30:15 INFO  🚀 开始执行分类器节点: classifier-002
2024-01-15 14:30:16 INFO  📝 构建分类提示词，类别数量: 3
2024-01-15 14:30:17 INFO  🤖 调用LLM进行分类: gpt-4
2024-01-15 14:30:19 INFO  ✅ LLM分类完成: tech-001 (置信度: 0.98)
2024-01-15 14:30:19 INFO  🔀 AI分类路由决策: classifier-002 -> tech-support-workflow
2024-01-15 14:30:19 INFO  🎯 路由到技术支持工作流
```

## 🚀 扩展可能性：真正的多Agent路由

虽然项目目前没有实现多Agent协作，但基于现有架构可以扩展：

### 1. Agent节点设计

```java
@Data
public class AgentNodeConfig {
    private String agentType;           // agent类型
    private String agentModel;          // 使用的模型
    private String systemPrompt;        // 系统提示词
    private List<String> tools;         // 可用工具
    private Map<String, Object> params; // 参数配置
}

public class AgentNode extends AbstractWfNode {
    @Override
    protected NodeProcessResult onProcess() {
        AgentNodeConfig config = checkAndGetConfig(AgentNodeConfig.class);

        // 1. 创建专门的Agent实例
        Agent agent = createAgent(config);

        // 2. 执行Agent任务
        AgentResult result = agent.execute(getInputs());

        // 3. 根据结果决定下一步
        return NodeProcessResult.builder()
            .nextNodeUuid(determineNextAgent(result))
            .content(result.getOutputs())
            .build();
    }
}
```

### 2. 动态Agent选择策略

```java
public class AgentRouterNode extends AbstractWfNode {
    @Override
    protected NodeProcessResult onProcess() {
        // 1. 分析当前任务复杂度
        TaskComplexity complexity = analyzeTask(getInputText());

        // 2. 评估可用Agent能力
        List<Agent> availableAgents = getAvailableAgents();

        // 3. 智能匹配最适合的Agent
        Agent selectedAgent = selectBestAgent(complexity, availableAgents);

        // 4. 路由到选中的Agent
        return NodeProcessResult.builder()
            .nextNodeUuid(selectedAgent.getNodeUuid())
            .build();
    }
}
```

### 3. Agent协作机制

```java
public class MultiAgentCoordinator {

    public void coordinateAgents(List<Agent> agents, Task task) {
        // 1. 任务分解
        List<SubTask> subTasks = decomposeTask(task);

        // 2. Agent分配
        Map<Agent, SubTask> assignments = assignTasks(agents, subTasks);

        // 3. 并行执行
        List<CompletableFuture<AgentResult>> futures = assignments.entrySet()
            .stream()
            .map(entry -> CompletableFuture.supplyAsync(() ->
                entry.getKey().execute(entry.getValue())))
            .toList();

        // 4. 结果聚合
        List<AgentResult> results = futures.stream()
            .map(CompletableFuture::join)
            .toList();

        // 5. 最终整合
        FinalResult finalResult = aggregateResults(results);
    }
}
```

## 🎯 总结

### 当前实现特点

1. **基于LLM的智能分类**：使用大语言模型进行内容理解和分类
2. **配置驱动的路由映射**：通过配置文件定义分类到节点的映射关系
3. **动态路由决策**：运行时根据分类结果动态选择下游节点
4. **可扩展的分类体系**：支持任意数量的分类类别和目标节点

### 与传统Agent路由的区别

- **当前**：基于内容分类的静态路由映射
- **传统Agent路由**：基于Agent能力和任务匹配的动态选择
- **扩展方向**：可以在现有架构基础上实现真正的多Agent协作

这种设计虽然不是传统的多Agent协作，但实现了智能化的工作流路由，能够根据输入内容的语义自动选择最合适的处理流程。通过扩展，完全可以实现更复杂的多Agent动态路由机制。
