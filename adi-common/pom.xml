<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.moyz</groupId>
        <artifactId>aideepin</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>adi-common</artifactId>

    <dependencies>
        <!-- 引入本地lib包 -->
        <dependency>
            <groupId>org.apache</groupId>
            <artifactId>age-jdbc</artifactId>
            <scope>system</scope>
            <version>1.0.0</version>
            <systemPath>${project.basedir}/src/lib/adi-age-jdbc.jar</systemPath>
        </dependency>
    </dependencies>
</project>