<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moyz.adi.common.mapper.WorkflowComponentMapper">
    <select id="countRefNodes" resultType="Integer">
        select count(1)
        from adi_workflow_component wfComponent
                 inner join adi_workflow_node wfNode on wfComponent.id = wfNode.workflow_component_id
        where wfComponent.uuid = #{uuid}
    </select>
</mapper>
