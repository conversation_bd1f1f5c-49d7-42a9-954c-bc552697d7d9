<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moyz.adi.common.mapper.KnowledgeBaseEmbeddingMapper">

    <select id="selectByItemUuid" resultType="com.moyz.adi.common.entity.KnowledgeBaseEmbedding">
        select *
        from <include refid="getTableName"/>
        where metadata ->> 'kb_item_uuid' = #{kbItemUuid}
    </select>

    <delete id="deleteByIds">
        delete from <include refid="getTableName"/> where embedding_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            <if test="id != null and id != ''">
                #{id}
            </if>
        </foreach>
    </delete>

    <delete id="deleteByItemUuid">
        delete
        from
        <include refid="getTableName"/>
        where metadata ->> 'kb_item_uuid' = #{kbItemUuid}
    </delete>

    <sql id="getTableName">
        <if test="tableSuffix != null and tableSuffix != ''">
            adi_knowledge_base_embedding_${tableSuffix}
        </if>
        <if test="tableSuffix == null or tableSuffix == ''">
            adi_knowledge_base_embedding
        </if>
    </sql>
</mapper>
