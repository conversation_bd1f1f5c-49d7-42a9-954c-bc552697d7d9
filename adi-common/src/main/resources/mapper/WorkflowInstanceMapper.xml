<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moyz.adi.common.mapper.WorkflowRunMapper">
    <select id="pageByWfUuid" resultType="com.moyz.adi.common.entity.WorkflowRuntime">
        select a.*
        from adi_workflow_instance a
                 left join adi_workflow b on a.workflow_id = b.id
        where a.is_deleted = false
          and a.user_id = #{userId}
          and b.uuid = #{wfUuid}
    </select>
</mapper>
