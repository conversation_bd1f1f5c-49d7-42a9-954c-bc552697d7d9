<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moyz.adi.common.mapper.ConversationMapper">

    <select id="countAllCreated" resultType="Integer">
        select count(1)
        from adi_conversation
        where is_deleted = false
    </select>

    <select id="countCreatedByTimePeriod" resultType="Integer">
        select count(1)
        from adi_conversation
        where is_deleted = false
          and create_time between #{beginTime} and #{endTime}
    </select>

</mapper>
