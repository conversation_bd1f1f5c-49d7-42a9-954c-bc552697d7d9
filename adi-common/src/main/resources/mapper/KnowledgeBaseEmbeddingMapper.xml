<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moyz.adi.common.mapper.KnowledgeBaseEmbeddingMapper">
    <select id="countByKbUuid" resultType="java.lang.Integer">
        select count(1)
        from adi_knowledge_base_embedding
        where metadata ->> 'kb_uuid' = #{kbUuid}
    </select>
</mapper>
