package com.moyz.adi.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import com.moyz.adi.common.base.JsonNodeTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "adi_draw", autoResultMap = true)
@Schema(title = "ai images", description = "Image generated by ai")
public class Draw extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField("uuid")
    private String uuid;

    @TableField("ai_model_id")
    private Long aiModelId;

    @TableField("ai_model_name")
    private String aiModelName;

    @TableField("prompt")
    private String prompt;

    @TableField("negative_prompt")
    private String negativePrompt;

    @TableField("generate_size")
    private String generateSize;

    @TableField("generate_quality")
    private String generateQuality;

    @TableField("generate_number")
    private Integer generateNumber;

    @TableField("generate_seed")
    private Integer generateSeed;

    @TableField(value = "dynamic_params", jdbcType = JdbcType.JAVA_OBJECT, typeHandler = JsonNodeTypeHandler.class)
    private JsonNode dynamicParams;

    @Schema(title = "file uuid")
    @TableField("original_image")
    private String originalImage;

    @Schema(title = "file uuid")
    @TableField("mask_image")
    private String maskImage;

    @TableField("resp_images_path")
    private String respImagesPath;

    @Schema(title = "generated image uuids")
    @TableField("generated_images")
    private String generatedImages;

    @TableField("interacting_method")
    private Integer interactingMethod;

    @TableField("process_status")
    private Integer processStatus;

    @TableField("process_status_remark")
    private String processStatusRemark;

    @TableField("is_public")
    private Boolean isPublic;

    @TableField("with_watermark")
    private Boolean withWatermark;

    @TableField("star_count")
    private Integer starCount;
}
