spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **************************************************************************************************************************************************
    username: your-db-account
    password: your-db-password
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      lettuce:
        pool:
          #连接池最大连接数
          max-active: 20
          #连接池最大阻塞等待时间
          max-wait: -1
          #连接池中的最大空闲连接
          max-idle: 5
          #连接池中的最小空闲连接
          min-idle: 1

adi:
  # embedding model可选值如下（见adi_ai_model中的embedding类型的数据）：
  # local:all-minilm-l6-v2 (default)
  # openai:text-embedding-3-small、openai:text-embedding-3-large
  # dashscope:text-embedding-v1、dashscope:text-embedding-v3
  # 注意：由于不同向量模型不同维度参数生成的向量特征空间不一致，无法兼容存储于一张表并进行混合搜索，建议项目初期确定好向量模型及其维度，后期如有修改，需重新生成向量数据
  embedding-model: local:all-minilm-l6-v2
  # vector-database: pgvector、neo4j
  vector-database: pgvector
  # graph-database: apache AGE、neo4j
  graph-database: apache-age
  datasource:
    neo4j:
      host: localhost
      port: 7687
      username: your-db-account
      password: your-db-password
      database: your-db-name