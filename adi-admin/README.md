# LangChain4j-AIDeepin 平台架构分析

## 项目概述

**LangChain4j-AIDeepin（得应AI）** 是一个基于Java技术栈的企业级AI工作效率提升平台，集成了多种AI能力，包括对话、图像生成、知识库问答、AI工作流等功能。

## 核心架构特点

### 1. 模块化设计
- **adi-common**: 公共模块，包含实体类、服务接口、工具类等
- **adi-chat**: 对话服务模块，处理AI对话相关功能
- **adi-admin**: 管理后台模块，提供系统管理功能
- **adi-bootstrap**: 启动模块，应用程序入口

### 2. 技术栈亮点
- **Spring Boot 3.4.2** + **JDK 17**: 现代化Java技术栈
- **LangChain4j**: Java版本的LangChain，提供AI能力集成
- **LangGraph4j**: 工作流编排引擎
- **PostgreSQL** + **pgvector**: 向量数据库支持
- **Apache AGE**: 图数据库扩展
- **Redis**: 缓存和会话管理

## 关键功能模块

### 1. AI对话服务 (ConversationMessageController)
- **多会话管理**: 支持多个对话会话并行
- **流式响应**: 使用SSE(Server-Sent Events)实现实时响应
- **多角色支持**: 可配置不同的AI角色和提示词
- **模型切换**: 支持多种LLM模型动态切换

### 2. 知识库服务 (KnowledgeBaseService)
- **RAG架构**: 检索增强生成，结合向量搜索和图搜索
- **文档处理**: 支持多种格式文档上传和解析
- **向量化存储**: 使用pgvector进行高效向量检索
- **知识图谱**: 基于Apache AGE构建实体关系图谱
- **混合检索**: 组合向量搜索和图搜索提高准确性

### 3. 图像生成服务 (DrawController)
- **多种生成模式**: 文生图、图生图、图像编辑
- **模型集成**: DALL-E 2/3、通义万相等
- **图像管理**: 支持图像收藏、评论、公开展示
- **批量处理**: 支持批量图像生成和管理

### 4. AI工作流引擎 (WorkflowEngine)
- **节点工厂模式**: 可扩展的节点类型系统
- **状态图执行**: 基于LangGraph4j的状态机执行
- **人机交互**: 支持工作流中的人工干预节点
- **条件分支**: 智能路由和条件判断
- **中断恢复**: 支持工作流暂停和恢复执行

### 5. MCP服务市场 (McpService)
- **服务管理**: MCP(Model Context Protocol)服务的统一管理
- **用户配置**: 个性化的MCP服务参数配置
- **工具调用**: 集成外部工具和API
- **多种传输**: 支持SSE和STDIO两种传输方式
- **安全加密**: 敏感参数的加密存储

## 设计模式与架构思想

### 1. 工厂模式
- **WfNodeFactory**: 工作流节点的统一创建工厂
- **LLMContext**: LLM服务的上下文管理
- **ImageModelContext**: 图像模型的上下文管理

### 2. 策略模式
- **AbstractLLMService**: 不同LLM提供商的统一接口
- **AbstractImageModelService**: 不同图像模型的统一接口
- **ContentRetriever**: 不同检索策略的统一接口

### 3. 观察者模式
- **SSEEmitterHelper**: 服务端推送事件管理
- **WorkflowEngine**: 工作流状态变化通知

### 4. 组合模式
- **CompositeRAG**: 组合多种RAG策略
- **WorkflowNode**: 工作流节点的树形结构

## 核心技术实现

### 1. 向量检索 (EmbeddingRAG)
```java
// 文档向量化和存储
DocumentSplitter documentSplitter = DocumentSplitters.recursive(
    RAG_MAX_SEGMENT_SIZE_IN_TOKENS, overlap,
    TokenEstimatorFactory.create(tokenEstimator)
);
EmbeddingStoreIngestor embeddingStoreIngestor = EmbeddingStoreIngestor.builder()
    .documentSplitter(documentSplitter)
    .embeddingModel(embeddingModel)
    .embeddingStore(embeddingStore)
    .build();
```

### 2. 知识图谱 (GraphRAG)
```java
// 实体关系抽取和图谱构建
GraphStoreIngestor ingestor = GraphStoreIngestor.builder()
    .documentSplitter(documentSplitter)
    .segmentsFunction(segments -> {
        // 实体和关系抽取逻辑
        return extractEntitiesAndRelations(segments);
    })
    .build();
```

### 3. 工作流执行 (WorkflowEngine)
```java
// 状态图构建和执行
StateGraph<WfNodeState> mainStateGraph = new StateGraph<>(stateSerializer);
mainStateGraph.addNode(nodeUuid, node_async((state) -> runNode(wfNode, state)));
CompiledGraph<WfNodeState> app = mainStateGraph.compile();
```

## 数据库设计亮点

### 1. 向量存储优化
- 使用pgvector扩展实现高效向量相似度搜索
- 支持多种距离计算方法（余弦、欧几里得等）
- 索引优化提升检索性能

### 2. 图数据库集成
- Apache AGE扩展提供图查询能力
- 支持复杂的图遍历和模式匹配
- 实体关系的高效存储和查询

### 3. 混合存储架构
- PostgreSQL主数据库存储结构化数据
- Redis缓存热点数据和会话信息
- 文件系统存储图像和文档

## 安全与性能

### 1. 安全措施
- JWT Token认证和授权
- 敏感数据AES加密存储
- API访问频率限制
- 用户权限分级管理

### 2. 性能优化
- 异步任务处理（@Async）
- 缓存策略（Redis）
- 连接池优化
- 分页查询和懒加载

## 扩展性设计

### 1. 插件化架构
- MCP服务的插件化集成
- 工作流节点的可扩展设计
- LLM模型的统一接口

### 2. 配置化管理
- 系统配置的动态加载
- 模型参数的灵活配置
- 用户个性化设置

## 部署与运维

### 1. 容器化部署
- Docker镜像构建
- docker-compose编排
- 环境变量配置

### 2. 监控与日志
- 应用性能监控
- 错误日志收集
- 业务指标统计

这个平台展现了现代AI应用开发的最佳实践，通过模块化设计、插件化架构和标准化接口，实现了高度可扩展和可维护的企业级AI解决方案。

## 架构图表

本项目提供了三个详细的架构流程图，使用Obsidian Advanced Canvas格式：

### 1. 平台整体架构图 (AIDeepin-Platform-Architecture.canvas)
- **主要模块关系**: 展示各个服务模块之间的依赖关系
- **数据流向**: 从用户请求到响应的完整数据流
- **技术栈分层**: 展示前端、后端、数据库的分层架构
- **工作流节点**: 详细展示AI工作流的各种节点类型
- **RAG处理流程**: 知识库的文档处理和检索流程

### 2. API调用流程图 (AIDeepin-API-Flow.canvas)
- **请求认证流程**: JWT Token验证和权限检查
- **核心API端点**: 对话、知识库、绘图等主要API
- **服务调用链**: LLM、RAG、图像服务的调用关系
- **数据库操作**: PostgreSQL、Redis的操作流程
- **响应处理**: SSE流式响应和错误处理

### 3. 工作流执行图 (AIDeepin-Workflow-Execution.canvas)
- **工作流生命周期**: 从启动到完成的完整流程
- **节点执行顺序**: 各种节点类型的执行逻辑
- **状态管理**: 工作流状态的跟踪和管理
- **错误处理**: 异常捕获和恢复机制
- **人机交互**: 工作流中断和用户输入处理

### 使用方法
1. 下载并安装 [Obsidian](https://obsidian.md/)
2. 安装 [Advanced Canvas](https://github.com/Developer-Mike/obsidian-advanced-canvas) 插件
3. 在Obsidian中打开对应的.canvas文件
4. 使用鼠标滚轮缩放，拖拽查看完整流程图

### 设计特点
- **颜色编码**: 不同模块使用不同颜色区分
- **箭头标注**: 每个连接都标注了调用意图和顺序
- **分组展示**: 相关功能模块进行分组显示
- **层次结构**: 主流程从左到右，子流程从上到下

这些图表帮助开发者快速理解系统架构，便于新人上手和系统维护。

## 关键技术亮点总结

### 1. 先进的RAG架构
- **混合检索策略**: 结合向量搜索和知识图谱，提供更准确的信息检索
- **智能文档分割**: 使用递归分割算法，保持语义完整性
- **动态上下文调整**: 根据模型token限制自动调整检索结果数量
- **多模态支持**: 支持文本、图像等多种数据类型的处理

### 2. 灵活的工作流引擎
- **可视化编排**: 支持拖拽式工作流设计
- **状态机执行**: 基于LangGraph4j的可靠状态管理
- **人机协作**: 支持工作流中的人工干预和决策
- **错误恢复**: 完善的异常处理和重试机制

### 3. 企业级安全设计
- **多层认证**: JWT + 权限控制 + API限流
- **数据加密**: 敏感信息AES加密存储
- **审计日志**: 完整的操作记录和追踪
- **资源隔离**: 用户数据和权限的严格隔离

### 4. 高性能架构
- **异步处理**: 大量使用@Async注解实现非阻塞操作
- **缓存策略**: Redis多级缓存提升响应速度
- **连接池优化**: 数据库连接池和HTTP连接池调优
- **流式响应**: SSE实现实时数据推送

### 5. 可扩展设计
- **插件化架构**: MCP协议支持第三方工具集成
- **模型抽象**: 统一的LLM和图像模型接口
- **配置驱动**: 系统行为通过配置文件灵活控制
- **微服务友好**: 模块化设计便于拆分为微服务

### 6. 开发体验优化
- **完整的API文档**: Swagger/OpenAPI 3.0规范
- **类型安全**: 充分利用Java强类型特性
- **异常处理**: 统一的异常处理和错误码管理
- **测试友好**: 良好的依赖注入和接口抽象

## 技术选型理由

### 为什么选择Java技术栈？
1. **企业级成熟度**: Java在企业应用中有丰富的生态和最佳实践
2. **性能优势**: JVM的优化和垃圾回收机制适合长期运行的服务
3. **团队技能**: Java开发人员相对容易招聘和培养
4. **生态丰富**: Spring Boot、MyBatis Plus等成熟框架

### 为什么选择PostgreSQL？
1. **向量支持**: pgvector扩展提供原生向量操作能力
2. **图数据库**: Apache AGE扩展支持图查询
3. **ACID特性**: 强一致性保证数据安全
4. **JSON支持**: 原生JSON类型适合存储配置数据

### 为什么选择LangChain4j？
1. **Java原生**: 避免Python调用的性能损耗
2. **类型安全**: 编译时错误检查
3. **生态集成**: 与Spring Boot无缝集成
4. **社区活跃**: 持续更新和维护

这个项目代表了Java生态在AI应用开发领域的最新实践，为企业级AI应用提供了完整的解决方案模板。

## 工作流编排系统详解

### 核心编排机制

LangChain4j-AIDeepin实现了一套完整的AI工作流编排系统，基于**LangGraph4j**状态机引擎，支持复杂的流程控制和业务逻辑编排。

#### 1. 工作流引擎架构
- **WorkflowEngine**: 核心执行引擎，负责工作流的编译、执行和状态管理
- **WfNodeFactory**: 节点工厂，统一创建和管理各种类型的工作流节点
- **WfState**: 工作流全局状态管理，包含执行上下文和节点关系
- **WfNodeState**: 单节点状态管理，继承自LangGraph4j的AgentState

#### 2. 节点类型体系

**控制流节点**
- `Start节点`: 工作流入口，初始化参数和开场白
- `End节点`: 工作流出口，结果模板渲染和变量替换
- `Switcher节点`: 条件分支控制，支持多条件组合(AND/OR)
- `Classifier节点`: AI智能分类，基于LLM的动态路由选择

**AI处理节点**
- `LLMAnswerNode`: 大语言模型调用，支持提示词模板和流式响应
- `KnowledgeRetrievalNode`: 知识库检索，向量搜索和阈值控制
- `Dalle3Node/TongyiwanxNode`: 图像生成，多平台支持和参数化配置

**数据处理节点**
- `TemplateNode`: 文本模板渲染和变量替换
- `KeywordExtractorNode`: AI关键词提取和结构化输出
- `DocumentExtractorNode`: 文档内容解析和多格式支持
- `FaqExtractorNode`: FAQ提取和知识库构建

**外部集成节点**
- `GoogleNode`: Google搜索集成和结果处理
- `HttpRequestNode`: HTTP API调用和参数模板化
- `MailSendNode`: 邮件发送和模板化内容
- `HumanFeedbackNode`: 人机交互和工作流中断恢复

#### 3. 流程控制机制

**顺序执行**
```
Start → LLM处理 → 模板渲染 → End
```
- 节点按定义顺序依次执行
- 上游输出自动作为下游输入
- 支持数据类型自动转换

**条件分支**
```java
// 配置示例
{
  "cases": [
    {
      "conditions": [
        {
          "nodeUuid": "upstream-node-id",
          "nodeParamName": "result",
          "operator": "contains",
          "value": "success"
        }
      ],
      "operator": "and",
      "targetNodeUuid": "success-path-node"
    }
  ],
  "defaultTargetNodeUuid": "default-path-node"
}
```

**并行执行**
```
Start → [并行分支A, 并行分支B, 并行分支C] → 汇聚节点 → End
```
- 多个分支同时执行，提高处理效率
- 结果自动汇聚合并
- 支持分支间数据共享

**人机交互**
```
Start → 处理 → HumanFeedback → 继续处理 → End
```
- 工作流可暂停等待用户输入
- 支持多次人机交互循环
- 状态持久化保证中断恢复

#### 4. 状态管理机制

**工作流级状态**
```java
public class WfState {
    private String uuid;                    // 工作流实例唯一标识
    private User user;                      // 执行用户上下文
    private List<NodeIOData> input;         // 工作流输入参数
    private List<NodeIOData> output;        // 工作流输出结果
    private Integer processStatus;          // 执行状态(就绪/执行中/成功/失败/等待输入)
    private Set<String> interruptNodes;     // 人机交互节点集合
    private Map<String, List<String>> edges; // 节点连接关系图
}
```

**节点级状态**
```java
public class WfNodeState extends AgentState {
    private String uuid;                    // 节点实例唯一标识
    private int processStatus;              // 节点处理状态
    private List<NodeIOData> inputs;        // 节点输入数据
    private List<NodeIOData> outputs;       // 节点输出数据
    private String processStatusRemark;     // 状态备注信息
}
```

#### 5. 执行流程详解

**1. 工作流编译阶段**
```java
// 构建节点关系树
buildCompileNode(rootCompileNode, startNode);

// 创建状态图
StateGraph<WfNodeState> mainStateGraph = new StateGraph<>(stateSerializer);

// 编译执行图
app = mainStateGraph.compile(compileConfig);
```

**2. 节点执行阶段**
```java
private Map<String, Object> runNode(WorkflowNode wfNode, WfNodeState nodeState) {
    // 1. 通过工厂创建节点实例
    AbstractWfNode abstractWfNode = WfNodeFactory.create(wfComponent, wfNode, wfState, nodeState);

    // 2. 执行节点逻辑
    NodeProcessResult processResult = abstractWfNode.process(inputConsumer, outputConsumer);

    // 3. 处理执行结果
    return processResult.toMap();
}
```

**3. 流程控制阶段**
```java
// 条件分支控制
stateGraph.addConditionalEdges(
    sourceNode,
    edge_async(state -> state.data().get("next").toString()),
    targetMappings
);

// 普通边连接
stateGraph.addEdge(sourceNode, targetNode);
```

**4. 中断恢复机制**
```java
public void resume(String userInput) {
    // 更新状态
    app.updateState(invokeConfig, Map.of(HUMAN_FEEDBACK_KEY, userInput), null);
    // 继续执行
    exe(invokeConfig, true);
}
```

#### 6. 错误处理与监控

**多层错误处理**
- 节点级异常捕获和状态更新
- 工作流级错误传播和资源清理
- 全局异常处理和错误消息推送

**实时监控机制**
- SSE实时状态推送
- 节点执行进度跟踪
- 性能指标监控

**状态持久化**
- Redis缓存工作流状态
- 数据库持久化执行历史
- 支持工作流暂停和恢复

这套工作流编排系统展现了现代AI应用中复杂业务逻辑的编排能力，通过可视化设计、状态机执行和插件化扩展，为企业级AI应用提供了强大的流程自动化能力。

## 节点创建与执行机制深度解析

### 节点生命周期管理

LangChain4j-AIDeepin实现了完整的节点生命周期管理，从定义、创建、实例化到执行的全流程控制。

#### 1. 四层架构设计

**组件定义层 (WorkflowComponent)**
```java
// 节点类型注册表，定义系统中可用的节点类型
@TableName("adi_workflow_component")
public class WorkflowComponent {
    private String name;           // 组件名称（如"Start", "Answer"）
    private String title;          // 显示标题
    private Boolean isEnable;      // 启用状态
    private Integer displayOrder;  // 显示顺序
}
```

**节点定义层 (WorkflowNode)**
```java
// 具体工作流中的节点实例配置
@TableName("adi_workflow_node")
public class WorkflowNode {
    private Long workflowComponentId;      // 关联组件类型
    private WfNodeInputConfig inputConfig; // 输入配置（JSONB）
    private ObjectNode nodeConfig;         // 节点配置（JSONB）
    private Double positionX, positionY;   // 画布位置
}
```

**运行时记录层 (WorkflowRuntimeNode)**
```java
// 节点执行过程中的状态和数据记录
@TableName("adi_workflow_runtime_node")
public class WorkflowRuntimeNode {
    private ObjectNode input;    // 节点输入数据
    private ObjectNode output;   // 节点输出数据
    private Integer status;      // 执行状态
}
```

**内存执行层 (AbstractWfNode)**
```java
// 节点执行时的内存对象实例
public abstract class AbstractWfNode {
    protected WorkflowComponent wfComponent;
    protected WorkflowNode node;
    protected WfState wfState;
    protected WfNodeState state;
}
```

#### 2. 节点创建流程

**自动创建Start节点**
```java
public WorkflowNode createStartNode(Workflow workflow) {
    // 1. 定义用户输入参数
    WfNodeIOText userInput = WfNodeIOText.builder()
        .name("var_user_input")
        .title("用户输入")
        .type(WfIODataTypeEnum.TEXT.getValue())
        .build();

    // 2. 构建输入配置
    WfNodeInputConfig inputConfig = new WfNodeInputConfig();
    inputConfig.setUserInputs(List.of(userInput));

    // 3. 创建节点实例
    WorkflowNode startNode = new WorkflowNode();
    startNode.setWorkflowComponentId(startComponent.getId());
    startNode.setInputConfig(inputConfig);

    return startNode;
}
```

**用户自定义节点创建**
- 前端拖拽组件到画布
- 配置节点参数和连接关系
- 后端验证并持久化配置
- 支持节点的增删改查操作

#### 3. 节点实例化机制

**工厂模式的类型安全创建**
```java
public class WfNodeFactory {
    public static AbstractWfNode create(WorkflowComponent component,
                                       WorkflowNode definition,
                                       WfState wfState,
                                       WfNodeState nodeState) {
        // 基于组件名称进行类型匹配
        switch (WfComponentNameEnum.getByName(component.getName())) {
            case START:
                return new StartNode(component, definition, wfState, nodeState);
            case LLM_ANSWER:
                return new LLMAnswerNode(component, definition, wfState, nodeState);
            case TEMPLATE:
                return new TemplateNode(component, definition, wfState, nodeState);
            // ... 其他节点类型
        }
    }
}
```

**运行时实例创建**
```java
// 每次节点执行时创建运行时记录
WfRuntimeNodeDto runtimeNode = workflowRuntimeNodeService.createByState(
    user, nodeDefinition.getId(), workflowRuntimeId, nodeState
);
```

#### 4. 节点执行机制

**统一的执行流程**
```java
public NodeProcessResult process(Consumer<WfNodeState> inputConsumer,
                               Consumer<WfNodeState> outputConsumer) {
    // 1. 状态设置：READY → DOING
    state.setProcessStatus(NODE_PROCESS_STATUS_DOING);

    // 2. 输入参数初始化
    initInput(); // 处理上游输出、参数引用、人机交互输入

    // 3. 执行输入处理回调
    if (inputConsumer != null) {
        inputConsumer.accept(state); // 更新数据库、发送SSE消息
    }

    // 4. 执行具体业务逻辑
    NodeProcessResult result = onProcess(); // 抽象方法，子类实现

    // 5. 处理执行结果
    state.setOutputs(result.getContent());
    state.setProcessStatus(NODE_PROCESS_STATUS_SUCCESS);

    // 6. 执行输出处理回调
    if (outputConsumer != null) {
        outputConsumer.accept(state); // 更新数据库、发送SSE消息
    }

    return result;
}
```

**具体节点实现示例**
```java
// StartNode - 工作流入口节点
public class StartNode extends AbstractWfNode {
    @Override
    public NodeProcessResult onProcess() {
        StartNodeConfig config = JsonUtil.fromJson(node.getNodeConfig(), StartNodeConfig.class);

        if (StringUtils.isNotBlank(config.getPrologue())) {
            // 输出开场白
            return NodeProcessResult.builder()
                .content(List.of(NodeIOData.createByText("output", "default", config.getPrologue())))
                .build();
        } else {
            // 直接传递输入到输出
            return NodeProcessResult.builder()
                .content(WfNodeIODataUtil.changeInputsToOutputs(state.getInputs()))
                .build();
        }
    }
}

// TemplateNode - 模板渲染节点
public class TemplateNode extends AbstractWfNode {
    @Override
    protected NodeProcessResult onProcess() {
        TemplateNodeConfig config = checkAndGetConfig(TemplateNodeConfig.class);

        // 处理文件内容为Markdown格式
        WfNodeIODataUtil.changeFilesContentToMarkdown(state.getInputs());

        // 渲染模板（支持变量替换）
        String content = WorkflowUtil.renderTemplate(config.getTemplate(), state.getInputs());

        return NodeProcessResult.builder()
            .content(List.of(NodeIOData.createByText("output", "", content)))
            .build();
    }
}
```

#### 5. 关键设计特点

**配置驱动的灵活性**
- 节点配置以JSONB格式存储，支持复杂参数结构
- 输入输出支持多种数据类型（文本、数字、布尔、文件、选项）
- 支持节点间的参数引用和数据传递

**类型安全的扩展性**
- 基于枚举的节点类型定义，编译时类型检查
- 工厂模式统一创建，支持新节点类型的动态注册
- 抽象基类提供通用功能，具体节点专注业务逻辑

**状态管理的完整性**
- 节点状态完整生命周期：READY → DOING → SUCCESS/FAIL
- 支持人机交互的中断和恢复机制
- 实时状态推送（SSE）和持久化存储

**错误处理的健壮性**
- 多层异常捕获和状态回滚
- 详细错误信息记录和传播
- 工作流级别的错误处理和资源清理

## 项目结构总览

### Maven多模块架构

LangChain4j-AIDeepin采用标准的Maven多模块项目结构，实现了清晰的模块分离和依赖管理。

#### 模块依赖关系
```
aideepin (父项目)
├── adi-common (公共核心模块)
├── adi-chat (对话服务模块) → 依赖 adi-common
├── adi-admin (管理后台模块) → 依赖 adi-common
└── adi-bootstrap (启动模块) → 聚合 adi-chat + adi-admin
```

#### 1. adi-bootstrap (启动模块)
- **职责**: 应用程序入口和依赖聚合
- **核心文件**: `BootstrapApplication.java`
- **配置管理**: `application.yml`、多环境配置
- **部署支持**: `Dockerfile`、`docker-compose.yml`
- **特性**: `@EnableAsync`、`@EnableScheduling`、`@EnableCaching`

#### 2. adi-chat (对话服务模块)
- **职责**: 用户端API接口和对话功能
- **主要控制器**:
  - `ConversationController` - 对话会话管理
  - `ConversationMessageController` - 消息处理和SSE流式响应
  - `DrawController` - 图像生成服务
  - `KnowledgeBaseController` - 知识库问答
  - `WorkflowController` - AI工作流执行

#### 3. adi-admin (管理后台模块)
- **职责**: 系统管理和配置接口
- **主要控制器**:
  - `AdminUserController` - 用户管理
  - `AdminModelController` - AI模型配置
  - `AdminMcpController` - MCP服务管理
  - `AdminConvController` - 对话管理
  - `AdminSysConfigController` - 系统配置

#### 4. adi-common (公共核心模块)
这是整个项目的核心模块，包含了所有的业务逻辑、数据访问和工具类。

**包结构详解**:

```
com.moyz.adi.common/
├── entity/           # 数据库实体类
│   ├── User, Conversation, ConversationMessage
│   ├── KnowledgeBase, KnowledgeBaseItem, KnowledgeBaseEmbedding
│   ├── Workflow, WorkflowNode, WorkflowComponent
│   ├── AiModel, Draw, Mcp
│   └── SysConfig, Prompt
├── service/          # 业务服务层
│   ├── LLM服务集成 (OpenAiLLMService, DeepSeekLLMService...)
│   ├── 知识库服务 (KnowledgeBaseService, KnowledgeBaseQaService...)
│   ├── 工作流服务 (WorkflowService, WorkflowNodeService...)
│   ├── 图像生成服务 (DrawService, OpenAiDalleService...)
│   └── 系统服务 (UserService, SysConfigService...)
├── workflow/         # 工作流引擎
│   ├── WorkflowEngine - 核心执行引擎
│   ├── WfNodeFactory - 节点工厂
│   ├── node/ - 各种节点实现
│   ├── WfState, WfNodeState - 状态管理
│   └── WorkflowStarter - 工作流启动器
├── rag/              # RAG实现
│   ├── EmbeddingRAG - 向量检索
│   ├── GraphRAG - 图检索
│   ├── CompositeRAG - 混合检索
│   ├── GraphStore - 图存储抽象
│   └── ApacheAgeGraphStore - AGE图存储实现
├── config/           # 配置管理
│   ├── BeanConfig - Bean配置
│   ├── WebMvcConfig - Web配置
│   ├── 各种TypeHandler - 数据类型处理
│   └── GlobalExceptionHandler - 全局异常处理
├── helper/           # 辅助工具
│   ├── LLMContext - LLM上下文管理
│   ├── ImageModelContext - 图像模型上下文
│   ├── SSEEmitterHelper - 服务端推送
│   └── QuotaHelper - 配额管理
├── interfaces/       # 抽象接口
│   ├── AbstractLLMService - LLM服务抽象
│   ├── AbstractImageModelService - 图像服务抽象
│   └── AbstractSearchEngineService - 搜索引擎抽象
├── util/             # 工具类库
│   ├── JsonUtil, AesUtil, RedisTemplateUtil
│   ├── 文件操作工具, 图像处理工具
│   └── 各种业务工具类
├── mapper/           # 数据访问层
│   └── MyBatis Mapper接口
├── dto/              # 数据传输对象
│   ├── 请求响应类
│   ├── 参数验证
│   └── 接口契约
└── enums/            # 枚举定义
    └── 各种业务枚举
```

### 数据库架构设计

#### PostgreSQL + 扩展
- **主数据库**: PostgreSQL 提供ACID事务保证
- **向量扩展**: pgvector 支持向量相似度搜索
- **图数据库扩展**: Apache AGE 支持图查询和知识图谱
- **JSONB支持**: 动态配置和复杂数据结构存储

#### 核心表设计
- **用户体系**: `adi_user`, `adi_user_day_cost`, `adi_user_mcp`
- **对话体系**: `adi_conversation`, `adi_conversation_message`, `adi_conversation_preset`
- **知识库体系**: `adi_knowledge_base`, `adi_knowledge_base_item`, `adi_knowledge_base_embedding`
- **工作流体系**: `adi_workflow`, `adi_workflow_node`, `adi_workflow_runtime`
- **AI模型体系**: `adi_ai_model`, `adi_draw`, `adi_mcp`

#### 数据库特性
- **JSONB字段**: 存储节点配置、动态参数等复杂数据
- **向量字段**: 使用pgvector存储文档向量
- **图数据**: 使用Apache AGE存储实体关系
- **触发器**: 自动更新时间戳和数据一致性维护

### 技术栈集成

#### 核心框架
- **Spring Boot 3.4.2**: 现代化Java应用框架
- **LangChain4j**: Java版LangChain，AI能力集成
- **LangGraph4j**: 工作流编排和状态机执行
- **MyBatis Plus**: ORM框架和数据访问

#### AI平台集成
- **LLM模型**: DeepSeek, OpenAI GPT, 通义千问, 文心一言
- **图像模型**: DALL-E 2/3, 通义万相, 硅基流动
- **搜索引擎**: Google Search API
- **本地模型**: Ollama支持

#### 存储和缓存
- **PostgreSQL**: 主数据库，支持pgvector和Apache AGE
- **Redis**: 缓存、会话存储、分布式锁
- **文件存储**: 阿里云OSS、本地文件系统

### 部署架构

#### 容器化部署
- **Docker**: 应用容器化
- **docker-compose**: 服务编排
- **环境变量**: 配置管理
- **健康检查**: 服务监控

#### 配置管理
- **多环境配置**: dev/prod环境分离
- **敏感信息加密**: AES加密存储
- **动态配置**: 支持运行时配置更新
- **缓存配置**: Redis配置缓存

这个项目结构展现了现代Java企业级应用的最佳实践，通过模块化设计、分层架构和标准化接口，实现了高度可维护和可扩展的AI应用平台。

## 完整业务流程解析

### 从前端接口到后端服务的完整链路

LangChain4j-AIDeepin项目实现了多个核心业务流程，每个流程都从前端接口开始，经过完整的后端处理链路，最终为用户提供AI服务。

#### 1. MCP服务集成流程
**从服务添加到工具调用的完整链路**

```
管理员添加MCP服务 → 用户配置MCP参数 → 对话中选择MCP → LLM调用工具 → 返回执行结果
```

- **服务注册**: `POST /admin/mcp/add` → 配置传输方式(SSE/STDIO) → 存储到adi_mcp表
- **用户配置**: `POST /user/mcp/saveOrUpdate` → 设置自定义参数 → 加密存储到adi_user_mcp表
- **对话集成**: 创建对话时指定mcpIds → 构建MCP客户端 → LLM自动调用工具
- **工具执行**: 匹配工具名称 → 执行MCP服务 → 处理结果 → 递归调用LLM

**关键技术特点**:
- 支持SSE和STDIO两种传输方式
- 参数AES加密存储保证安全性
- 无缝集成到LLM工具调用机制
- 完善的错误处理和容错机制

#### 2. 知识库RAG检索流程
**从文档上传到智能问答的完整链路**

```
创建知识库 → 上传文档 → 向量化+图谱化 → RAG检索 → 增强生成 → 返回答案
```

- **知识库创建**: `POST /knowledge-base/saveOrUpdate` → 配置RAG参数 → 存储到adi_knowledge_base表
- **文档处理**: `POST /knowledge-base/upload/{uuid}` → 文档解析 → 创建知识条目
- **索引处理**: 异步向量化(pgvector) + 图谱化(Apache AGE) → 双重索引存储
- **RAG检索**: `POST /knowledge-base/qa/add/{kbUuid}` → 混合检索 → 构建增强提示词 → LLM生成答案

**关键技术特点**:
- EmbeddingRAG + GraphRAG + CompositeRAG混合检索策略
- 异步处理避免阻塞，实时状态跟踪
- 支持多种文档格式和智能分割
- 向量存储和图存储的双重索引

#### 3. 对话消息处理流程
**从用户输入到AI响应的完整链路**

```
创建对话 → 发送消息 → SSE连接 → LLM调用 → 流式响应 → 保存消息
```

- **对话创建**: `POST /conversation/add` → 配置AI模型、MCP服务、知识库 → 存储对话配置
- **消息处理**: `POST /conversation/message/process` → 建立SSE连接 → 异步处理消息
- **参数构建**: 加载历史消息 → 创建MCP客户端 → 集成RAG检索 → 构建ChatModelParams
- **LLM调用**: 流式响应处理 → 工具调用处理 → 实时SSE推送 → 保存问答记录

**关键技术特点**:
- SSE流式响应提供实时体验
- 支持多模态输入(文本+图像)
- MCP工具调用和RAG检索无缝集成
- 完整的消息历史管理

#### 4. 工作流编排执行流程
**从可视化设计到自动化执行的完整链路**

```
创建工作流 → 配置节点 → 连接边 → 执行工作流 → 状态机运行 → 返回结果
```

- **工作流创建**: `POST /workflow/add` → 自动创建Start节点 → 存储工作流定义
- **节点配置**: `POST /workflow/update` → 拖拽添加节点 → 配置参数 → 连接边关系
- **工作流执行**: `POST /workflow/run/{wfUuid}` → 加载配置 → 构建状态图 → LangGraph4j执行
- **节点运行**: 工厂创建节点实例 → 执行业务逻辑 → SSE推送状态 → 流程控制

**关键技术特点**:
- 基于LangGraph4j的状态机执行引擎
- 支持条件分支、并行执行、人机交互
- 可视化编排和配置驱动
- 完整的中断恢复机制

### 核心设计模式应用

#### 1. 工厂模式
- **WfNodeFactory**: 统一创建各种工作流节点
- **LLMContext**: 管理多种LLM服务实例
- **ImageModelContext**: 管理多种图像模型

#### 2. 策略模式
- **AbstractLLMService**: 不同LLM提供商的统一接口
- **ContentRetriever**: 多种RAG检索策略
- **McpTransport**: SSE和STDIO传输方式

#### 3. 观察者模式
- **SSEEmitterHelper**: 实时状态推送
- **WorkflowEngine**: 工作流状态变化通知

#### 4. 组合模式
- **CompositeRAG**: 组合多种检索策略
- **WorkflowNode**: 工作流节点的树形结构

### 数据流转机制

#### 1. 配置数据流
```
前端配置 → JSON序列化 → JSONB存储 → 运行时反序列化 → 业务逻辑执行
```

#### 2. 消息数据流
```
用户输入 → 历史消息加载 → ChatMessage构建 → LLM调用 → 响应处理 → 消息存储
```

#### 3. 工作流数据流
```
用户输入 → 节点输入处理 → 业务逻辑执行 → 节点输出生成 → 下游节点传递
```

### 性能优化策略

#### 1. 异步处理
- 所有耗时操作使用@Async异步执行
- SSE非阻塞流式响应
- 工作流节点并行执行

#### 2. 缓存机制
- Redis缓存热点数据
- 组件定义缓存(@Cacheable)
- LLM服务实例缓存

#### 3. 数据库优化
- pgvector向量索引优化
- 复合索引设计
- 分页查询和懒加载

这些完整的业务流程展现了现代AI应用的复杂性和系统性，通过标准化的接口设计、模块化的架构和先进的技术栈，实现了高度集成和可扩展的AI服务平台。

### 相关文档
- `MCP服务添加配置与使用完整流程.md` - MCP服务集成完整链路
- `知识库创建文档处理与RAG检索完整流程.md` - 知识库RAG系统详解
- `对话创建消息处理与SSE流式响应完整流程.md` - 对话系统完整实现
- `工作流创建配置与执行完整流程.md` - 工作流引擎详细解析
- `MCP服务完整流程图.canvas` - MCP服务流程可视化图
- `AIDeepin-Project-Structure.canvas` - 项目整体结构图
- `AIDeepin-Module-Dependencies.canvas` - 模块依赖关系图
- `AIDeepin-Database-Structure.canvas` - 数据库结构设计图
- `AIDeepin-Workflow-Orchestration-Summary.md` - 工作流编排详细技术文档
- `AIDeepin-Node-Creation-Execution-Analysis.md` - 节点创建执行机制深度分析
- `AIDeepin-Workflow-Nodes-Flow.canvas` - 节点类型和流转关系图
- `AIDeepin-Node-Lifecycle.canvas` - 节点生命周期管理图