{"nodes": [{"id": "user-request", "type": "text", "x": -1000, "y": -600, "width": 200, "height": 120, "color": "1", "text": "# 👤 用户请求\n\n**输入：**\n- 工作流UUID\n- 用户输入参数\n- HTTP POST请求"}, {"id": "controller-entry", "type": "text", "x": -700, "y": -600, "width": 200, "height": 120, "color": "2", "text": "# 🎯 控制器入口\n\n**WorkflowController**\n- 参数验证\n- 用户认证\n- 创建SSE连接"}, {"id": "async-startup", "type": "text", "x": -400, "y": -600, "width": 200, "height": 120, "color": "3", "text": "# 🚀 异步启动\n\n**WorkflowStarter**\n- @Async异步执行\n- 工作流状态检查\n- 资源初始化"}, {"id": "engine-init", "type": "text", "x": -100, "y": -600, "width": 200, "height": 120, "color": "4", "text": "# ⚙️ 引擎初始化\n\n**WorkflowEngine**\n- 加载节点和边定义\n- 创建WfState状态\n- 查找开始/结束节点"}, {"id": "compile-tree", "type": "text", "x": -1000, "y": -400, "width": 200, "height": 140, "color": "5", "text": "# 🌳 编译节点树\n\n**buildCompileNode()**\n- 递归构建CompileNode\n- 识别并行分支\n- 处理条件分支\n- 创建GraphCompileNode"}, {"id": "build-stategraph", "type": "text", "x": -700, "y": -400, "width": 200, "height": 140, "color": "6", "text": "# 📊 构建状态图\n\n**buildStateGraph()**\n- 添加异步节点\n- 建立边连接\n- 创建子图\n- 配置条件边"}, {"id": "langgraph-compile", "type": "text", "x": -400, "y": -400, "width": 200, "height": 140, "color": "1", "text": "# 🔗 LangGraph编译\n\n**StateGraph.compile()**\n- 生成CompiledGraph\n- 配置检查点保存器\n- 设置中断节点\n- 优化执行路径"}, {"id": "execution-start", "type": "text", "x": -100, "y": -400, "width": 200, "height": 140, "color": "2", "text": "# 🎬 开始执行\n\n**app.stream()**\n- 创建AsyncGenerator\n- 初始化执行状态\n- 启动流式处理\n- 返回NodeOutput迭代器"}, {"id": "node-execution", "type": "text", "x": -1000, "y": -200, "width": 200, "height": 160, "color": "3", "text": "# 🔧 节点执行\n\n**runNode()**\n- WfNodeFactory创建实例\n- 发送NODE_RUN事件\n- 执行process()方法\n- 处理输入输出回调\n- 更新节点状态"}, {"id": "streaming-process", "type": "text", "x": -700, "y": -200, "width": 200, "height": 160, "color": "4", "text": "# 🌊 流式处理\n\n**streamingResult()**\n- 迭代NodeOutput\n- 识别StreamingOutput\n- 实时推送数据块\n- 发送NODE_CHUNK事件"}, {"id": "sse-push", "type": "text", "x": -400, "y": -200, "width": 200, "height": 160, "color": "5", "text": "# 📡 SSE推送\n\n**SSEEmitterHelper**\n- parseAndSendPartialMsg\n- 事件类型标识\n- 数据格式化\n- 错误处理"}, {"id": "state-update", "type": "text", "x": -100, "y": -200, "width": 200, "height": 160, "color": "6", "text": "# 📊 状态更新\n\n**WfState管理**\n- 更新completedNodes\n- 记录runtimeNodes\n- 保存输入输出\n- 更新processStatus"}, {"id": "condition-check", "type": "text", "x": -1000, "y": 0, "width": 200, "height": 140, "color": "1", "text": "# 🔀 条件检查\n\n**条件分支处理**\n- SwitcherNode条件判断\n- ClassifierNode AI分类\n- 动态路由选择\n- 下一节点确定"}, {"id": "parallel-execution", "type": "text", "x": -700, "y": 0, "width": 200, "height": 140, "color": "2", "text": "# ⚡ 并行执行\n\n**子图并行处理**\n- GraphCompileNode\n- 独立子图编译\n- 并行任务调度\n- 结果汇聚"}, {"id": "interrupt-handle", "type": "text", "x": -400, "y": 0, "width": 200, "height": 140, "color": "3", "text": "# ⏸️ 中断处理\n\n**人机交互节点**\n- 检测中断节点\n- 保存执行状态\n- 发送等待事件\n- 暂停执行流程"}, {"id": "resume-execution", "type": "text", "x": -100, "y": 0, "width": 200, "height": 140, "color": "4", "text": "# ▶️ 恢复执行\n\n**用户输入后**\n- 接收用户反馈\n- 恢复执行状态\n- 继续流程处理\n- 更新节点状态"}, {"id": "completion", "type": "text", "x": -1000, "y": 200, "width": 200, "height": 120, "color": "5", "text": "# ✅ 执行完成\n\n**工作流结束**\n- 到达END节点\n- 生成最终输出\n- 关闭SSE连接"}, {"id": "error-handling", "type": "text", "x": -700, "y": 200, "width": 200, "height": 120, "color": "6", "text": "# ❌ 错误处理\n\n**异常处理机制**\n- 节点执行异常\n- 状态回滚\n- 错误事件推送\n- 资源清理"}, {"id": "data-persistence", "type": "text", "x": -400, "y": 200, "width": 200, "height": 120, "color": "1", "text": "# 💾 数据持久化\n\n**运行时记录**\n- WorkflowRuntime\n- WfRuntimeNode\n- 执行日志\n- 性能指标"}, {"id": "frontend-display", "type": "text", "x": -100, "y": 200, "width": 200, "height": 120, "color": "2", "text": "# 🖥️ 前端展示\n\n**实时更新**\n- 节点状态显示\n- 流式内容展示\n- 进度条更新\n- 结果呈现"}], "edges": [{"id": "flow-1", "fromNode": "user-request", "fromSide": "right", "toNode": "controller-entry", "toSide": "left", "color": "1", "label": "1. HTTP请求"}, {"id": "flow-2", "fromNode": "controller-entry", "fromSide": "right", "toNode": "async-startup", "toSide": "left", "color": "2", "label": "2. 异步调用"}, {"id": "flow-3", "fromNode": "async-startup", "fromSide": "right", "toNode": "engine-init", "toSide": "left", "color": "3", "label": "3. 引擎启动"}, {"id": "flow-4", "fromNode": "engine-init", "fromSide": "bottom", "toNode": "compile-tree", "toSide": "top", "color": "4", "label": "4. 编译准备"}, {"id": "flow-5", "fromNode": "compile-tree", "fromSide": "right", "toNode": "build-stategraph", "toSide": "left", "color": "5", "label": "5. 构建状态图"}, {"id": "flow-6", "fromNode": "build-stategraph", "fromSide": "right", "toNode": "langgraph-compile", "toSide": "left", "color": "6", "label": "6. <PERSON><PERSON><PERSON><PERSON>编译"}, {"id": "flow-7", "fromNode": "langgraph-compile", "fromSide": "right", "toNode": "execution-start", "toSide": "left", "color": "1", "label": "7. 开始执行"}, {"id": "flow-8", "fromNode": "execution-start", "fromSide": "bottom", "toNode": "node-execution", "toSide": "top", "color": "2", "label": "8. 节点执行"}, {"id": "flow-9", "fromNode": "node-execution", "fromSide": "right", "toNode": "streaming-process", "toSide": "left", "color": "3", "label": "9. 流式处理"}, {"id": "flow-10", "fromNode": "streaming-process", "fromSide": "right", "toNode": "sse-push", "toSide": "left", "color": "4", "label": "10. SSE推送"}, {"id": "flow-11", "fromNode": "sse-push", "fromSide": "right", "toNode": "state-update", "toSide": "left", "color": "5", "label": "11. 状态更新"}, {"id": "flow-12", "fromNode": "state-update", "fromSide": "bottom", "toNode": "condition-check", "toSide": "top", "color": "6", "label": "12. 条件检查"}, {"id": "flow-13", "fromNode": "condition-check", "fromSide": "right", "toNode": "parallel-execution", "toSide": "left", "color": "1", "label": "13. 并行分支"}, {"id": "flow-14", "fromNode": "parallel-execution", "fromSide": "right", "toNode": "interrupt-handle", "toSide": "left", "color": "2", "label": "14. 中断检测"}, {"id": "flow-15", "fromNode": "interrupt-handle", "fromSide": "right", "toNode": "resume-execution", "toSide": "left", "color": "3", "label": "15. 恢复执行"}, {"id": "flow-16", "fromNode": "resume-execution", "fromSide": "bottom", "toNode": "completion", "toSide": "top", "color": "4", "label": "16. 完成检查"}, {"id": "flow-17", "fromNode": "completion", "fromSide": "right", "toNode": "error-handling", "toSide": "left", "color": "5", "label": "17. 异常处理"}, {"id": "flow-18", "fromNode": "error-handling", "fromSide": "right", "toNode": "data-persistence", "toSide": "left", "color": "6", "label": "18. 数据保存"}, {"id": "flow-19", "fromNode": "data-persistence", "fromSide": "right", "toNode": "frontend-display", "toSide": "left", "color": "1", "label": "19. 前端展示"}, {"id": "loop-1", "fromNode": "node-execution", "fromSide": "bottom", "toNode": "node-execution", "toSide": "left", "color": "2", "label": "循环执行节点"}, {"id": "feedback-1", "fromNode": "sse-push", "fromSide": "top", "toNode": "controller-entry", "toSide": "bottom", "color": "3", "label": "实时反馈"}]}