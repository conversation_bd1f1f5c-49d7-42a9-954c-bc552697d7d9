# 工作流数据流转 T5-T14 步骤的异步处理机制深度解析

## 一、关键步骤的详细分解

### T5: streamingInvokeLLM() 启动 LLM 调用

```java
public static void streamingInvokeLLM(WfState wfState, WfNodeState state, WorkflowNode node, String modelName, List<ChatMessage> msgs) {
    log.info("stream invoke");
    AbstractLLMService<?> llmService = LLMContext.getLLMServiceByName(modelName);
    StreamingChatGenerator<AgentState> streamingGenerator = StreamingChatGenerator.builder()
            .mapResult(response -> {
                String responseTxt = response.aiMessage().text();
                NodeIOData output = NodeIOData.createByText(DEFAULT_OUTPUT_PARAM_NAME, "", responseTxt);
                wfState.getNodeStateByNodeUuid(node.getUuid()).ifPresent(item -> item.getOutputs().add(output));
                return Map.of("completeResult", responseTxt);
            })
            .build();
    
    streamingLLM.chat(request, streamingGenerator.handler());  // 🔥 关键：启动异步LLM调用
    wfState.getNodeToStreamingGenerator().put(node.getUuid(), streamingGenerator);  // 🔥 注册生成器
    //LLM返回的chunk存放在阻塞队列中，此处不做处理，交由WorkflowEngine统一处理
}
```

**T5 步骤的异步机制：**

1. **创建 StreamingGenerator**：包装 LLM 响应处理逻辑
2. **启动异步 LLM 调用**：`streamingLLM.chat()` 立即返回，LLM 在后台异步执行
3. **注册生成器**：将生成器存储到 `wfState` 中，供后续使用
4. **方法立即返回**：不等待 LLM 完成

### T6: LLM 节点开始执行，发送 [NODE_INPUT_nodeId] 事件

```java
@Override
public NodeProcessResult onProcess() {
    LLMAnswerNodeConfig nodeConfigObj = checkAndGetConfig(LLMAnswerNodeConfig.class);
    String prompt = WorkflowUtil.renderTemplate(nodeConfigObj.getPrompt(), state.getInputs());
    String modelName = nodeConfigObj.getModelName();
    //调用LLM
    WorkflowUtil.streamingInvokeLLM(wfState, state, node, modelName, List.of(UserMessage.from(prompt)));
    return new NodeProcessResult();  // 🔥 立即返回空结果
}
```

**T6 步骤的关键点：**

1. **同步执行部分**：参数准备、配置解析等
2. **异步调用启动**：调用 `streamingInvokeLLM()`
3. **立即返回**：返回空的 `NodeProcessResult`，不等待 LLM 完成

### T7: 调用 streamingInvokeLLM()，启动 LLM 调用

这一步已经在 T5 中详细分析了，关键是理解这里的"启动"不是"等待完成"。

### T8: runNode() 立即返回，节点注册 StreamingGenerator

```java
private Map<String, Object> runNode(WorkflowNode wfNode, WfNodeState nodeState) {
    // 4. 🚀 执行节点的核心业务逻辑
    NodeProcessResult processResult = abstractWfNode.process(/*...*/);  // 🔥 这里会调用 LLMAnswerNode.onProcess()
    
    // 7. 📺 处理流式输出（如果有）
    StreamingChatGenerator<AgentState> generator = wfState.getNodeToStreamingGenerator().get(wfNode.getUuid());
    if (null != generator) {
        resultMap.put("_streaming_messages", generator);  // 🔥 关键：将生成器放入返回结果
    }
    
    return resultMap;  // 🔥 立即返回，包含生成器引用
}
```

**T8 步骤的核心机制：**

1. **同步执行**：`abstractWfNode.process()` 同步执行，但内部启动了异步 LLM 调用
2. **生成器检索**：从 `wfState` 中获取之前注册的 `StreamingGenerator`
3. **生成器传递**：将生成器放入 `resultMap` 的 `_streaming_messages` 字段
4. **立即返回**：`runNode()` 方法立即返回，不等待 LLM 完成

## 二、LangGraph4j 框架的检测和处理机制

### T9: LangGraph4j 检测到流式生成器，开始监听

这是 LangGraph4j 框架内部的机制，当 `node_async` 包装的函数返回包含 `_streaming_messages` 字段的 Map 时：

```java
// LangGraph4j 内部逻辑（伪代码）
Map<String, Object> nodeResult = nodeFunction.apply(state);
if (nodeResult.containsKey("_streaming_messages")) {
    StreamingChatGenerator generator = (StreamingChatGenerator) nodeResult.get("_streaming_messages");
    // 框架开始监听这个生成器的输出
    startListeningToGenerator(generator, nodeId);
}
```

**T9 步骤的框架行为：**

1. **结果检查**：框架检查节点返回结果中是否包含流式生成器
2. **监听启动**：如果发现生成器，开始监听其输出
3. **事件转换**：将生成器的输出转换为 `StreamingOutput` 事件

### T10: LLM 产生 chunk，通过 StreamingGenerator 传递

```java
// StreamingChatGenerator 内部机制（基于 LangGraph4j）
streamingLLM.chat(request, new StreamingChatResponseHandler() {
    @Override
    public void onPartialResponse(String partialResponse) {
        // 🔥 LLM 产生 chunk 时，放入内部阻塞队列
        generator.offerChunk(nodeId, partialResponse);
    }
});
```

**T10 步骤的数据流：**

1. **LLM 产生 chunk**：LLM 在独立线程中产生数据块
2. **队列存储**：chunk 被放入 `StreamingGenerator` 的内部阻塞队列
3. **事件生成**：框架将 chunk 包装成 `StreamingOutput` 事件

### T11: WorkflowEngine.streamingResult() 接收到 StreamingOutput

```java
private void streamingResult(WfState wfState, AsyncGenerator<NodeOutput<WfNodeState>> outputs, SseEmitter sseEmitter) {
    for (NodeOutput<WfNodeState> out : outputs) {  // 🔥 阻塞等待下一个输出
        if (out instanceof StreamingOutput<WfNodeState> streamingOutput) {
            String node = streamingOutput.node();
            String chunk = streamingOutput.chunk();
            log.info("node:{},chunk:{}", node, streamingOutput.chunk());
            SSEEmitterHelper.parseAndSendPartialMsg(sseEmitter, "[NODE_CHUNK_" + node + "]", chunk);
        } else {
            // 处理节点完成事件
        }
    }
}
```

**T11 步骤的处理机制：**

1. **阻塞等待**：主线程在 `for` 循环中阻塞等待下一个输出
2. **类型判断**：判断输出是 `StreamingOutput`（流式数据）还是普通 `NodeOutput`（完成事件）
3. **实时推送**：将流式数据立即推送给前端

## 三、异步数据流和同步控制流的协调

### 数据流的双重性质

```
同步控制流：
runNode() → process() → streamingInvokeLLM() → return NodeProcessResult → runNode() return

异步数据流：
LLM Thread → StreamingGenerator → BlockingQueue → AsyncGenerator → streamingResult()
```

**关键协调机制：**

1. **控制流立即返回**：节点执行的控制流立即完成，不阻塞工作流进度
2. **数据流异步产生**：LLM 数据在后台异步产生，通过队列传递
3. **主线程响应式等待**：主线程等待数据流，而不是控制流

### T12: 发送 [NODE_CHUNK_nodeId] 事件到前端

这一步在 T11 中已经包含，每个 chunk 都会立即推送给前端。

### T13: LLM 完成，产生最终 NodeOutput

```java
// LLM 完成时的处理
@Override
public void onCompleteResponse(ChatResponse response) {
    // 1. 更新节点状态
    String responseTxt = response.aiMessage().text();
    NodeIOData output = NodeIOData.createByText(DEFAULT_OUTPUT_PARAM_NAME, "", responseTxt);
    nodeState.getOutputs().add(output);
    
    // 2. 生成完成事件
    generator.complete();  // 🔥 这会产生最终的 NodeOutput 事件
}
```

**T13 步骤的完成机制：**

1. **状态更新**：将 LLM 的完整响应添加到节点状态
2. **完成信号**：生成器发出完成信号
3. **事件产生**：框架产生最终的 `NodeOutput` 事件

### T14: 发送 [NODE_OUTPUT_nodeId] 事件

```java
// 输出回调：记录和发送输出数据
(outputState) -> {
    workflowRuntimeNodeService.updateOutput(runtimeNodeDto.getId(), nodeState);
    String nodeUuid = wfNode.getUuid();
    List<NodeIOData> nodeOutputs = nodeState.getOutputs();
    for (NodeIOData output : nodeOutputs) {
        SSEEmitterHelper.parseAndSendPartialMsg(sseEmitter, "[NODE_OUTPUT_" + nodeUuid + "]", JsonUtil.toJson(output));
    }
}
```

**T14 步骤的输出处理：**

1. **状态更新**：更新数据库中的节点运行状态
2. **输出推送**：将节点的最终输出推送给前端
3. **完成标记**：标记节点执行完成

## 四、异步处理的核心要点

### 1. 三层异步架构

```
第一层：HTTP 异步 (@Async)
第二层：节点执行异步 (node_async)
第三层：LLM 调用异步 (StreamingChatModel)
```

### 2. 数据流和控制流分离

- **控制流**：节点的执行逻辑，同步完成
- **数据流**：LLM 的响应数据，异步产生

### 3. 阻塞队列作为桥梁

`StreamingGenerator` 内部的阻塞队列是连接异步数据生产者（LLM）和同步数据消费者（主线程）的关键桥梁。

### 4. 响应式等待模式

主线程不是"不等待"，而是"响应式等待"：
- 有数据就处理
- 没数据就等待
- 不会无限期阻塞（有超时机制）

### 5. node_async 包装的真正作用

```java
node_async((state) -> runNode(wfNode, state))
```

这个异步包装的核心作用是：

1. **支持流式生成器返回**：允许节点返回包含 `_streaming_messages` 的结果
2. **框架集成**：让 LangGraph4j 框架能够识别和处理流式输出
3. **状态管理**：支持节点状态的异步更新和传递
4. **并发执行**：在并行分支中支持真正的并发执行

## 五、总结

T5-T14 步骤的异步处理机制的核心是：

1. **T5-T8**：启动异步操作，立即返回控制权
2. **T9**：框架检测并开始监听异步数据流
3. **T10-T12**：异步数据流实时产生和消费
4. **T13-T14**：异步操作完成，发送完成事件

这种设计实现了：
- **非阻塞的节点执行**：节点逻辑不会因为 LLM 调用而阻塞
- **实时的数据推送**：用户可以实时看到 LLM 的响应过程
- **统一的流式处理**：所有异步数据都通过统一的机制处理

异步处理的关键不在于"不等待"，而在于"分离关注点"：控制流负责工作流的推进，数据流负责实时的用户体验。

`node_async` 包装是这整个异步机制的关键入口，它不仅仅是简单的异步包装，更是连接同步节点逻辑和异步数据流的重要桥梁。
