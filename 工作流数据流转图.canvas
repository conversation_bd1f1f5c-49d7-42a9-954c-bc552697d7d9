{"nodes": [{"id": "user-input", "type": "text", "x": -1000, "y": -500, "width": 200, "height": 120, "color": "1", "text": "# 👤 用户输入\n\n**数据来源：**\n- HTTP请求体\n- JSON格式参数\n- 多类型数据支持\n\n**示例：**\n```json\n{\"user_question\": \"如何重置密码？\"}\n```"}, {"id": "wf-state-init", "type": "text", "x": -700, "y": -500, "width": 200, "height": 120, "color": "2", "text": "# 📊 WfState初始化\n\n**全局状态创建：**\n- 用户会话信息\n- 输入参数封装\n- 运行时UUID\n- 节点关系映射\n\n**状态管理：**\n- completedNodes\n- runtimeNodes\n- processStatus"}, {"id": "start-node-process", "type": "text", "x": -400, "y": -500, "width": 200, "height": 120, "color": "3", "text": "# 🎬 Start节点处理\n\n**输入处理：**\n- 接收工作流输入\n- 开场白配置\n- 参数验证\n\n**输出生成：**\n- NodeIOData封装\n- 传递给下游节点"}, {"id": "node-io-data", "type": "text", "x": -100, "y": -500, "width": 200, "height": 120, "color": "4", "text": "# 📥📤 NodeIOData\n\n**数据结构：**\n- name: 参数名称\n- type: 数据类型\n- content: 实际内容\n- metadata: 元数据\n\n**类型支持：**\n- TEXT, FILE, JSON\n- 自动类型转换"}, {"id": "classifier-process", "type": "text", "x": -1000, "y": -300, "width": 200, "height": 140, "color": "5", "text": "# 🔍 Classifier节点\n\n**AI分类处理：**\n- LLM智能分析\n- 类别匹配\n- 路由决策\n\n**输出格式：**\n```json\n{\n  \"categoryUuid\": \"tech-support\",\n  \"confidence\": 0.95\n}\n```"}, {"id": "knowledge-retrieval", "type": "text", "x": -700, "y": -300, "width": 200, "height": 140, "color": "6", "text": "# 🔍 知识检索节点\n\n**检索过程：**\n- 向量化查询\n- 相似度搜索\n- 阈值过滤\n- 结果聚合\n\n**输出内容：**\n- 检索到的知识片段\n- 相关度分数\n- 默认响应处理"}, {"id": "llm-answer-process", "type": "text", "x": -400, "y": -300, "width": 200, "height": 140, "color": "1", "text": "# 🤖 LLM回答节点\n\n**流式处理：**\n- 提示词模板渲染\n- 流式响应生成\n- 实时数据推送\n\n**数据流：**\n- StreamingGenerator\n- 逐块数据输出\n- SSE实时推送"}, {"id": "template-render", "type": "text", "x": -100, "y": -300, "width": 200, "height": 140, "color": "2", "text": "# 📝 模板渲染节点\n\n**模板处理：**\n- 变量替换\n- 格式化输出\n- Markdown转换\n\n**模板语法：**\n```\n根据检索结果：{kb_result}\n生成回答：{llm_answer}\n```"}, {"id": "switcher-condition", "type": "text", "x": -1000, "y": -100, "width": 200, "height": 140, "color": "3", "text": "# 🔀 Switcher条件判断\n\n**条件检查：**\n- 参数引用\n- 条件比较\n- 逻辑运算(AND/OR)\n\n**路由决策：**\n- 满足条件→目标节点\n- 不满足→默认路径\n- 多条件组合判断"}, {"id": "parallel-branches", "type": "text", "x": -700, "y": -100, "width": 200, "height": 140, "color": "4", "text": "# ⚡ 并行分支处理\n\n**子图执行：**\n- GraphCompileNode\n- 独立状态管理\n- 并行任务调度\n\n**结果汇聚：**\n- 多分支结果合并\n- 状态同步\n- 下游节点传递"}, {"id": "human-feedback", "type": "text", "x": -400, "y": -100, "width": 200, "height": 140, "color": "5", "text": "# 👥 人机交互节点\n\n**中断机制：**\n- 执行暂停\n- 等待用户输入\n- 状态保存\n\n**恢复处理：**\n- 用户反馈接收\n- 状态恢复\n- 继续执行流程"}, {"id": "end-node-output", "type": "text", "x": -100, "y": -100, "width": 200, "height": 140, "color": "6", "text": "# 🏁 End节点输出\n\n**结果处理：**\n- 模板渲染\n- 最终输出生成\n- 工作流完成标记\n\n**输出格式：**\n- 结构化结果\n- 用户友好展示\n- 元数据包含"}, {"id": "sse-streaming", "type": "text", "x": -1000, "y": 100, "width": 200, "height": 120, "color": "1", "text": "# 📡 SSE流式推送\n\n**事件类型：**\n- NODE_RUN_* 节点开始\n- NODE_CHUNK_* 流式数据\n- NODE_INPUT_* 输入数据\n- NODE_OUTPUT_* 输出数据\n\n**实时推送机制**"}, {"id": "runtime-persistence", "type": "text", "x": -700, "y": 100, "width": 200, "height": 120, "color": "2", "text": "# 💾 运行时持久化\n\n**数据存储：**\n- WorkflowRuntime\n- WfRuntimeNode\n- 输入输出记录\n- 执行状态快照\n\n**性能指标统计**"}, {"id": "state-snapshot", "type": "text", "x": -400, "y": 100, "width": 200, "height": 120, "color": "3", "text": "# 📸 状态快照\n\n**检查点机制：**\n- MemorySaver\n- 状态序列化\n- 断点恢复\n- 中断处理\n\n**状态管理：**\n- 自动保存\n- 版本控制"}, {"id": "frontend-display", "type": "text", "x": -100, "y": 100, "width": 200, "height": 120, "color": "4", "text": "# 🖥️ 前端展示\n\n**实时更新：**\n- 节点状态显示\n- 进度条更新\n- 流式内容展示\n- 结果呈现\n\n**用户交互：**\n- 输入反馈\n- 状态监控"}], "edges": [{"id": "data-flow-1", "fromNode": "user-input", "fromSide": "right", "toNode": "wf-state-init", "toSide": "left", "color": "1", "label": "1. 输入封装"}, {"id": "data-flow-2", "fromNode": "wf-state-init", "fromSide": "right", "toNode": "start-node-process", "toSide": "left", "color": "2", "label": "2. 状态初始化"}, {"id": "data-flow-3", "fromNode": "start-node-process", "fromSide": "right", "toNode": "node-io-data", "toSide": "left", "color": "3", "label": "3. 数据封装"}, {"id": "data-flow-4", "fromNode": "node-io-data", "fromSide": "bottom", "toNode": "classifier-process", "toSide": "top", "color": "4", "label": "4. 分类处理"}, {"id": "data-flow-5", "fromNode": "classifier-process", "fromSide": "right", "toNode": "knowledge-retrieval", "toSide": "left", "color": "5", "label": "5. 知识检索"}, {"id": "data-flow-6", "fromNode": "knowledge-retrieval", "fromSide": "right", "toNode": "llm-answer-process", "toSide": "left", "color": "6", "label": "6. LL<PERSON>处理"}, {"id": "data-flow-7", "fromNode": "llm-answer-process", "fromSide": "right", "toNode": "template-render", "toSide": "left", "color": "1", "label": "7. 模板渲染"}, {"id": "data-flow-8", "fromNode": "template-render", "fromSide": "bottom", "toNode": "switcher-condition", "toSide": "top", "color": "2", "label": "8. 条件判断"}, {"id": "data-flow-9", "fromNode": "switcher-condition", "fromSide": "right", "toNode": "parallel-branches", "toSide": "left", "color": "3", "label": "9. 并行分支"}, {"id": "data-flow-10", "fromNode": "parallel-branches", "fromSide": "right", "toNode": "human-feedback", "toSide": "left", "color": "4", "label": "10. 人机交互"}, {"id": "data-flow-11", "fromNode": "human-feedback", "fromSide": "right", "toNode": "end-node-output", "toSide": "left", "color": "5", "label": "11. 最终输出"}, {"id": "streaming-flow-1", "fromNode": "llm-answer-process", "fromSide": "bottom", "toNode": "sse-streaming", "toSide": "top", "color": "6", "label": "流式数据推送"}, {"id": "streaming-flow-2", "fromNode": "sse-streaming", "fromSide": "right", "toNode": "runtime-persistence", "toSide": "left", "color": "1", "label": "数据持久化"}, {"id": "streaming-flow-3", "fromNode": "runtime-persistence", "fromSide": "right", "toNode": "state-snapshot", "toSide": "left", "color": "2", "label": "状态快照"}, {"id": "streaming-flow-4", "fromNode": "state-snapshot", "fromSide": "right", "toNode": "frontend-display", "toSide": "left", "color": "3", "label": "前端展示"}, {"id": "feedback-loop-1", "fromNode": "frontend-display", "fromSide": "top", "toNode": "human-feedback", "toSide": "bottom", "color": "4", "label": "用户反馈"}, {"id": "state-update-1", "fromNode": "wf-state-init", "fromSide": "bottom", "toNode": "runtime-persistence", "toSide": "top", "color": "5", "label": "状态更新"}]}