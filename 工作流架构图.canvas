{"nodes": [{"id": "frontend-interface", "type": "text", "x": -800, "y": -400, "width": 280, "height": 200, "color": "1", "text": "# 🌐 前端接口层\n\n**WorkflowController**\n- `/workflow/run/{wfUuid}` (SSE)\n- 接收用户输入参数\n- 返回SseEmitter流式连接\n- 支持TEXT_EVENT_STREAM媒体类型\n\n**核心功能：**\n- 工作流触发入口\n- 参数验证和转换\n- SSE连接管理"}, {"id": "workflow-starter", "type": "text", "x": -400, "y": -400, "width": 280, "height": 200, "color": "2", "text": "# 🚀 工作流启动器\n\n**WorkflowStarter**\n- streaming() 创建SSE连接\n- @Async asyncRun() 异步执行\n- 工作流状态检查\n- 用户权限验证\n\n**核心功能：**\n- 异步任务调度\n- 资源初始化\n- 错误处理和回滚"}, {"id": "workflow-engine", "type": "text", "x": 0, "y": -400, "width": 300, "height": 220, "color": "3", "text": "# ⚙️ 工作流引擎\n\n**WorkflowEngine**\n- run() 主执行方法\n- 节点树构建 buildCompileNode()\n- 状态图构建 buildStateGraph()\n- LangGraph4j集成编译\n\n**核心功能：**\n- 声明式配置解析\n- 状态图编译优化\n- 执行流程控制\n- 中断和恢复机制"}, {"id": "state-management", "type": "text", "x": 400, "y": -400, "width": 280, "height": 200, "color": "4", "text": "# 📊 状态管理层\n\n**WfState (工作流状态)**\n- 全局执行上下文\n- 节点关系映射\n- 中断节点管理\n- 流式生成器缓存\n\n**WfNodeState (节点状态)**\n- 继承AgentState\n- 输入输出数据\n- 执行状态跟踪"}, {"id": "node-factory", "type": "text", "x": -800, "y": 0, "width": 280, "height": 240, "color": "5", "text": "# 🏭 节点工厂\n\n**WfNodeFactory**\n- 统一节点创建入口\n- 支持15+节点类型\n- 动态实例化\n\n**节点类型：**\n- 控制流：Start/End/Switcher/Classifier\n- AI处理：LLM/KnowledgeRetrieval/ImageGen\n- 数据处理：Template/KeywordExtractor\n- 外部集成：HTTP/Mail/Google"}, {"id": "node-types", "type": "text", "x": -400, "y": 0, "width": 280, "height": 240, "color": "6", "text": "# 🔧 节点类型体系\n\n**AbstractWfNode (基类)**\n- process() 统一执行接口\n- 输入输出回调机制\n- 状态生命周期管理\n\n**具体实现：**\n- LLMAnswerNode: 流式LLM调用\n- KnowledgeRetrievalNode: 向量检索\n- SwitcherNode: 条件分支控制\n- ClassifierNode: AI智能分类"}, {"id": "compile-process", "type": "text", "x": 0, "y": 0, "width": 300, "height": 240, "color": "1", "text": "# 🔄 编译过程\n\n**CompileNode树构建**\n- 递归解析节点关系\n- 并行分支识别\n- GraphCompileNode子图处理\n\n**StateGraph构建**\n- node_async() 异步包装\n- edge_async() 条件路由\n- 子图编译和嵌套\n- 中断点配置"}, {"id": "execution-engine", "type": "text", "x": 400, "y": 0, "width": 280, "height": 240, "color": "2", "text": "# 🚀 执行引擎\n\n**CompiledGraph**\n- 节点执行器映射表\n- 边路由器映射表\n- 状态序列化器\n- 检查点保存器\n\n**执行特性：**\n- 异步并行执行\n- 流式数据处理\n- 状态持久化\n- 动态路由选择"}, {"id": "sse-helper", "type": "text", "x": -800, "y": 400, "width": 280, "height": 200, "color": "3", "text": "# 📡 SSE助手\n\n**SSEEmitterHelper**\n- startSse() 连接初始化\n- parseAndSendPartialMsg() 消息推送\n- 限流和状态检查\n- 错误处理和清理\n\n**事件类型：**\n- NODE_RUN_* 节点开始\n- NODE_CHUNK_* 流式数据\n- NODE_INPUT/OUTPUT_* 数据流转"}, {"id": "streaming-output", "type": "text", "x": -400, "y": 400, "width": 280, "height": 200, "color": "4", "text": "# 🌊 流式输出\n\n**StreamingOutput处理**\n- AsyncGenerator迭代消费\n- 实时数据推送\n- 节点状态通知\n- Token统计和计费\n\n**数据流：**\n- LLM → StreamingGenerator\n- Generator → NodeOutput\n- NodeOutput → SSE推送"}, {"id": "langgraph4j", "type": "text", "x": 0, "y": 400, "width": 300, "height": 200, "color": "5", "text": "# 🔗 LangGraph4j集成\n\n**核心能力：**\n- StateGraph状态图构建\n- 异步节点执行包装\n- 条件边路由机制\n- 检查点状态保存\n- 中断和恢复支持\n\n**编译优化：**\n- 声明式→命令式转换\n- 并行执行优化\n- 内存管理"}, {"id": "data-persistence", "type": "text", "x": 400, "y": 400, "width": 280, "height": 200, "color": "6", "text": "# 💾 数据持久化\n\n**WorkflowRuntime**\n- 执行实例记录\n- 输入输出存储\n- 状态快照保存\n\n**WfRuntimeNode**\n- 节点执行记录\n- 性能指标统计\n- 错误日志追踪\n\n**MemorySaver**\n- 检查点状态保存"}], "edges": [{"id": "edge-1", "fromNode": "frontend-interface", "fromSide": "right", "toNode": "workflow-starter", "toSide": "left", "color": "1", "label": "1. HTTP请求触发"}, {"id": "edge-2", "fromNode": "workflow-starter", "fromSide": "right", "toNode": "workflow-engine", "toSide": "left", "color": "2", "label": "2. 异步启动引擎"}, {"id": "edge-3", "fromNode": "workflow-engine", "fromSide": "right", "toNode": "state-management", "toSide": "left", "color": "3", "label": "3. 初始化状态"}, {"id": "edge-4", "fromNode": "workflow-engine", "fromSide": "bottom", "toNode": "compile-process", "toSide": "top", "color": "4", "label": "4. 编译节点树"}, {"id": "edge-5", "fromNode": "compile-process", "fromSide": "right", "toNode": "execution-engine", "toSide": "left", "color": "5", "label": "5. 生成执行图"}, {"id": "edge-6", "fromNode": "node-factory", "fromSide": "right", "toNode": "node-types", "toSide": "left", "color": "6", "label": "6. 创建节点实例"}, {"id": "edge-7", "fromNode": "node-types", "fromSide": "top", "toNode": "workflow-engine", "toSide": "bottom", "color": "1", "label": "7. 节点注册"}, {"id": "edge-8", "fromNode": "execution-engine", "fromSide": "bottom", "toNode": "streaming-output", "toSide": "top", "color": "2", "label": "8. 流式执行"}, {"id": "edge-9", "fromNode": "streaming-output", "fromSide": "left", "toNode": "sse-helper", "toSide": "right", "color": "3", "label": "9. SSE推送"}, {"id": "edge-10", "fromNode": "sse-helper", "fromSide": "top", "toNode": "frontend-interface", "toSide": "bottom", "color": "4", "label": "10. 返回前端"}, {"id": "edge-11", "fromNode": "compile-process", "fromSide": "bottom", "toNode": "langgraph4j", "toSide": "top", "color": "5", "label": "11. LangGrap<PERSON>4j编译"}, {"id": "edge-12", "fromNode": "execution-engine", "fromSide": "bottom", "toNode": "data-persistence", "toSide": "top", "color": "6", "label": "12. 状态持久化"}]}