{"nodes": [{"id": "workflow-definition", "type": "text", "x": -1000, "y": -600, "width": 250, "height": 160, "color": "1", "text": "# 📋 工作流定义\n\n**声明式配置：**\n- WorkflowNode节点定义\n- WorkflowEdge边关系\n- WorkflowComponent组件\n- JSON配置参数\n\n**数据库存储：**\n- 节点配置表\n- 边关系表\n- 组件定义表"}, {"id": "compile-node-tree", "type": "text", "x": -600, "y": -600, "width": 250, "height": 160, "color": "2", "text": "# 🌳 CompileNode树构建\n\n**buildCompileNode():**\n- 递归解析节点关系\n- 识别并行分支\n- 创建GraphCompileNode\n- 构建节点依赖树\n\n**树结构：**\n- 单一上游→普通节点\n- 多上游→并行分支节点"}, {"id": "graph-compile-node", "type": "text", "x": -200, "y": -600, "width": 250, "height": 160, "color": "3", "text": "# 🔀 GraphCompileNode\n\n**并行分支处理：**\n- root: 分支起始节点\n- branches: 并行执行路径\n- tail: 分支汇聚节点\n\n**子图管理：**\n- 独立状态图创建\n- 并行执行调度\n- 结果汇聚机制"}, {"id": "state-graph-build", "type": "text", "x": 200, "y": -600, "width": 250, "height": 160, "color": "4", "text": "# 📊 StateGraph构建\n\n**buildStateGraph():**\n- 添加异步节点包装\n- 建立边连接关系\n- 创建条件边路由\n- 配置子图嵌套\n\n**LangGraph4j集成：**\n- StateGraph<WfNodeState>\n- node_async()包装\n- edge_async()路由"}, {"id": "node-async-wrapper", "type": "text", "x": -1000, "y": -300, "width": 250, "height": 180, "color": "5", "text": "# 🔄 node_async包装\n\n**异步执行函数：**\n```java\nnode_async(state -> runNode(wfNode, state))\n```\n\n**包装功能：**\n- 节点实例创建\n- 输入输出回调\n- 状态更新管理\n- 异常处理机制\n\n**执行上下文：**\n- WfNodeState传递\n- 结果Map返回"}, {"id": "edge-async-router", "type": "text", "x": -600, "y": -300, "width": 250, "height": 180, "color": "6", "text": "# 🛤️ edge_async路由\n\n**条件路由函数：**\n```java\nedge_async(state -> \n  state.data().get(\"next\").toString())\n```\n\n**路由机制：**\n- 动态路径选择\n- 状态驱动决策\n- 条件表达式求值\n- 多分支支持\n\n**映射配置：**\n- 目标节点映射表"}, {"id": "subgraph-compilation", "type": "text", "x": -200, "y": -300, "width": 250, "height": 180, "color": "1", "text": "# 🔗 子图编译\n\n**并行子图处理：**\n- 独立StateGraph创建\n- 子图内部编译\n- 嵌套到主图中\n\n**编译优化：**\n```java\nStateGraph<WfNodeState> subgraph = \n  new StateGraph<>(stateSerializer);\nCompiledGraph compiled = \n  subgraph.compile();\n```\n\n**性能优化：**\n- 并行执行调度\n- 资源隔离管理"}, {"id": "compile-config", "type": "text", "x": 200, "y": -300, "width": 250, "height": 180, "color": "2", "text": "# ⚙️ 编译配置\n\n**CompileConfig设置：**\n- checkpointSaver: MemorySaver\n- interruptBefore: 中断节点列表\n- stateSerializer: 状态序列化器\n\n**中断机制：**\n- 人机交互节点识别\n- 自动中断点配置\n- 状态保存和恢复\n\n**检查点管理：**\n- 状态持久化\n- 断点恢复支持"}, {"id": "compiled-graph", "type": "text", "x": -1000, "y": 0, "width": 250, "height": 200, "color": "3", "text": "# 🎯 CompiledGraph\n\n**编译结果：**\n- nodeExecutors: 节点执行器映射\n- edgeRouters: 边路由器映射\n- topology: 图拓扑结构\n- checkpointSaver: 状态保存器\n\n**执行能力：**\n- 异步并行执行\n- 动态路由选择\n- 状态管理\n- 中断恢复\n\n**性能优化：**\n- 预编译优化\n- 内存管理"}, {"id": "execution-optimization", "type": "text", "x": -600, "y": 0, "width": 250, "height": 200, "color": "4", "text": "# ⚡ 执行优化\n\n**声明式→命令式转换：**\n- 配置解析为可执行代码\n- 依赖关系自动处理\n- 并行执行路径优化\n\n**编译时优化：**\n- 死代码消除\n- 路径合并优化\n- 资源预分配\n\n**运行时优化：**\n- 异步任务调度\n- 内存池管理\n- 状态缓存机制"}, {"id": "langgraph4j-integration", "type": "text", "x": -200, "y": 0, "width": 250, "height": 200, "color": "5", "text": "# 🔗 LangGraph4j集成\n\n**核心能力借用：**\n- StateGraph状态图构建\n- AsyncNodeAction异步执行\n- AsyncEdgeAction条件路由\n- CheckpointSaver状态保存\n\n**框架优势：**\n- 成熟的状态管理\n- 高效的异步执行\n- 完善的错误处理\n- 灵活的扩展机制\n\n**集成方式：**\n- 适配器模式\n- 组合模式"}, {"id": "streaming-support", "type": "text", "x": 200, "y": 0, "width": 250, "height": 200, "color": "6", "text": "# 🌊 流式支持\n\n**AsyncGenerator集成：**\n- 流式数据生产\n- 实时数据消费\n- 背压控制机制\n\n**StreamingOutput处理：**\n- 数据块识别\n- 实时推送机制\n- 缓冲区管理\n\n**性能特性：**\n- 低延迟响应\n- 内存高效\n- 并发安全\n\n**应用场景：**\n- LLM流式输出\n- 实时数据处理"}], "edges": [{"id": "compile-flow-1", "fromNode": "workflow-definition", "fromSide": "right", "toNode": "compile-node-tree", "toSide": "left", "color": "1", "label": "1. 解析配置"}, {"id": "compile-flow-2", "fromNode": "compile-node-tree", "fromSide": "right", "toNode": "graph-compile-node", "toSide": "left", "color": "2", "label": "2. 并行分支识别"}, {"id": "compile-flow-3", "fromNode": "graph-compile-node", "fromSide": "right", "toNode": "state-graph-build", "toSide": "left", "color": "3", "label": "3. 状态图构建"}, {"id": "compile-flow-4", "fromNode": "state-graph-build", "fromSide": "bottom", "toNode": "node-async-wrapper", "toSide": "top", "color": "4", "label": "4. 节点包装"}, {"id": "compile-flow-5", "fromNode": "node-async-wrapper", "fromSide": "right", "toNode": "edge-async-router", "toSide": "left", "color": "5", "label": "5. 边路由配置"}, {"id": "compile-flow-6", "fromNode": "edge-async-router", "fromSide": "right", "toNode": "subgraph-compilation", "toSide": "left", "color": "6", "label": "6. 子图编译"}, {"id": "compile-flow-7", "fromNode": "subgraph-compilation", "fromSide": "right", "toNode": "compile-config", "toSide": "left", "color": "1", "label": "7. 编译配置"}, {"id": "compile-flow-8", "fromNode": "compile-config", "fromSide": "bottom", "toNode": "compiled-graph", "toSide": "top", "color": "2", "label": "8. 生成编译图"}, {"id": "compile-flow-9", "fromNode": "compiled-graph", "fromSide": "right", "toNode": "execution-optimization", "toSide": "left", "color": "3", "label": "9. 执行优化"}, {"id": "compile-flow-10", "fromNode": "execution-optimization", "fromSide": "right", "toNode": "langgraph4j-integration", "toSide": "left", "color": "4", "label": "10. 框架集成"}, {"id": "compile-flow-11", "fromNode": "langgraph4j-integration", "fromSide": "right", "toNode": "streaming-support", "toSide": "left", "color": "5", "label": "11. 流式支持"}]}